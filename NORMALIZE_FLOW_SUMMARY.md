# LaTeX规范化脚本完整流程总结

## 概述

`normalize.py` 是一个经过多轮优化的LaTeX规范化脚本，采用**双阶段处理架构**：JavaScript AST处理 + Python规则处理，确保LaTeX公式的高质量规范化。

## 整体架构

```
原始LaTeX → [JavaScript AST处理] → [同义词替换] → [Python规则处理] → 规范化LaTeX
```

### 运行模式
- **tokenize模式**: 仅进行JavaScript AST处理和同义词替换
- **normalize模式**: 完整的规范化流程

## 第一阶段：JavaScript AST处理 (`tokenize_latex`)

### 1. 预处理保护
```python
# 保护文本命令，避免被JavaScript修改
\text{...} → \textprotected{N}
\mbox{...} → \text{CONTENTPROTECTED{N}}  # 同时转换为text
\hbox{...} → \text{CONTENTPROTECTED{N}}
```

### 2. 环境统一（可选）
```python
# 根据配置决定是否统一环境
\begin{split|align|alignedat|alignat|eqnarray} → \begin{aligned}
\begin{smallmatrix} → \begin{matrix}
```

### 3. JavaScript AST处理
- 调用 `js_scripts/preprocess_formula.js`
- 使用KaTeX解析器进行AST级别的token化
- 处理复杂的LaTeX结构和嵌套

### 4. 操作符名称规范化
```python
# 将\operatorname转换为简化形式
\operatorname{sin} → \sin
\operatorname{cos} → \cos
# ... 其他数学函数
```

### 5. 保护块恢复
```python
# 恢复被保护的文本块
\textprotected{N} → 原始\text{...}
CONTENTPROTECTED{N} → 原始内容
```

## 第二阶段：同义词替换 (`replace_synonym_tokens`)

### 1. 配置文件读取
- 读取 `token_unify.csv` 配置文件
- 建立同义词到标准token的映射

### 2. 智能替换
```python
# 按长度降序排序，避免短token误匹配
# 执行字符串替换，统一同义词token
```

## 第三阶段：Python规则处理 (`normalize_latex`)

### 步骤0：文本命令保护
```python
# 保护\text{}, \mbox{}, \hbox{}命令不被后续处理修改
\text{...} → __PROTECTED_TEXT_N__
```

### 步骤1：尾部清理（可选）
```python
# 移除LaTeX公式尾部的间距和装饰命令
\hspace{...}, \vspace{...}, \quad, \qquad, ~, . 等
```

### 步骤2：基础命令替换
```python
\pmatrix → \mypmatrix
\matrix → \mymatrix
```

### 步骤3：移除对齐命令
```python
\raggedright, \arraybackslash → (删除)
```

### 步骤4：移除大小写转换命令
```python
\lowercase, \uppercase → (删除)
```

### 步骤5-7：Token合并处理

#### 步骤5：空格命令处理
```python
\hspace { 1 0 p t } → \hspace{10pt}
\vspace { 5 m m } → \vspace{5mm}
```

#### 步骤6：数组格式处理
```python
\begin{array} { l r c } → \begin{array}{lrc}
```

#### 步骤7：复杂Token合并
- **\string命令合并**: `\string a b c` → `\stringabc`
- **大型定界符合并**: `\Big [ ` → `\Big[`
- **\operatorname*处理**: `\operatorname *` → `\operatorname`
- **重音符号合并**: `\' a ` → `\'a`
- **\parbox命令合并**: `\parbox {width}` → `\parbox{width}`
- **\raisebox命令合并**: 复杂的盒子命令处理
- **\char字符命令合并**: `{ \char123 }` → `{\char123}`
- **\rule线条命令合并**: `\rule {1pt} {2pt}` → `\rule{1pt}{2pt}`
- **\specialrule处理**: 特殊线条命令合并

### 步骤8：颜色命令移除
```python
# 移除所有颜色相关命令
\colorbox{...}, \color{...}, \textcolor{...}, \cellcolor{...} → (删除)
```

### 步骤9：括号补全处理

#### 单参数Token处理
```python
# ONE_Tail_Tokens: \hat, \tilde, \overline, \underline 等
\hat x → \hat{x}
\tilde a → \tilde{a}
```

#### 双参数Token处理
```python
# TWO_Tail_Tokens: \frac, \binom
\frac a b → \frac{a}{b}
\binom n k → \binom{n}{k}

# 智能括号匹配，处理嵌套结构
\frac{\frac{a}{b}}{c} → 正确处理嵌套
```

#### 特殊Token处理
```python
# AB_Tail_Tokens: \sqrt, \xrightarrow, \xleftarrow
\sqrt[n] x → \sqrt[n]{x}
\xrightarrow[below]{above} → 正确处理可选和必需参数
```

### 步骤10：数学函数转换
```python
# 将\operatorname转换为\mathrm格式
\operatorname{sin} → \mathrm{s i n}
\operatorname{cos} → \mathrm{c o s}
# 处理带空格和不带空格的两种格式
```

### 步骤11：数学函数名展开
```python
# 直接的函数名转换
\sin → \mathrm{s i n}
\cos → \mathrm{c o s}
\tan → \mathrm{t a n}
\log → \mathrm{l o g}
\exp → \mathrm{e x p}
\ln → \mathrm{l n}
# ... 包括三角函数、双曲函数、反三角函数、其他数学函数
```

### 步骤12：空格清理 (`clean_latex_spaces`)

#### 转义空格修复（关键修复）
```python
# 修复JavaScript tokenize阶段产生的转义空格问题
\  = → =     # 转义等号
\  { → {     # 转义花括号
\  } → }
\  [ → [     # 转义方括号
\  ] → ]
\  ( → (     # 转义圆括号
\  ) → )
\  , → ,     # 转义逗号
\  + → +     # 转义运算符
\  - → -
\  * → *
\  / → /
\  < → <
\  > → >

# 通用转义空格清理
\ + 非字母字符 → 非字母字符
```

#### 结构性空格清理
```python
# 花括号空格清理
{ } → {}
\s*{\s* → {
\s*} → }

# 方括号空格清理
[ ] → []

# 圆括号空格清理
( ) → ()

# 运算符空格清理
+ - = < > * / ^ _ → 移除前后空格

# 标点符号空格清理
, ; : . → 移除前后空格
```

#### 数学字体命令清理
```python
# \mathrm内部空格清理
\mathrm { s i n h } → \mathrm{sinh}

# 其他数学字体命令
\mathbf, \mathit, \mathcal, \mathbb, \mathfrak, \mathsf, \mathtt
```

### 步骤13：连字符和省略号处理
```python
# 连字符展开
--- → - - -
-- → - -

# 省略号统一化
… → . . .
\ldots → . . .
\hdots → . . .
\cdots → . . .
\dots → . . .
# ... 其他省略号命令
```

### 步骤14：空花括号删除
```python
# 删除空花括号，包括只包含空格的
{} → (删除)
{ } → (删除)
# 多次执行处理嵌套空花括号
```

### 步骤15：必要空格添加 (`add_required_spaces`)

#### 命令保护机制
```python
# 保护长命令避免被短命令破坏
\upsilon, \Upsilon → 保护，避免被\up匹配
\iota → 保护，避免被\it匹配
# 使用占位符机制
```

#### 格式命令空格处理
```python
# 需要尾随空格的命令（已移除\up避免冲突）
\bf, \it, \rm, \sf, \tt, \sc, \em, \sl
\tiny, \scriptsize, \footnotesize, \small, \normalsize
\large, \Large, \LARGE, \huge, \Huge
\displaystyle, \textstyle, \scriptstyle, \scriptscriptstyle
# 使用严格的边界检查，避免误匹配
```

#### 数学符号空格处理
```python
# 希腊字母、数学符号后跟字母时添加空格
\alpha t → \alpha t
\Delta x → \Delta x
# 使用改进的正则表达式，确保只匹配完整命令
```

#### 特殊处理
```python
# \mathrm{} 后跟字母添加空格
\mathrm{sin}x → \mathrm{sin} x
```

### 步骤16：保护块恢复
```python
# 恢复被保护的\text{}块
__PROTECTED_TEXT_N__ → 原始\text{...}
```

## 关键修复和优化

### 1. 转义空格问题修复
- **问题**: JavaScript tokenize产生的`\  =`被错误处理为`\=`
- **解决**: 在空格清理开头添加转义空格预处理
- **影响**: 解决了花括号渲染失败的核心问题

### 2. 命令冲突问题修复
- **问题**: 短命令`\up`匹配长命令`\upsilon`前缀
- **解决**: 移除`\up`，增强保护机制，改进正则表达式
- **影响**: 确保希腊字母等长命令不被破坏

### 3. 括号补全优化
- **智能匹配**: 使用`find_matching_brace`函数处理嵌套结构
- **完整性保证**: 确保每个需要参数的命令都有正确的括号
- **边界处理**: 正确处理各种边界情况

### 4. 文本保护机制
- **多层保护**: tokenize阶段和normalize阶段都有保护
- **格式转换**: `\mbox`和`\hbox`自动转换为`\text`
- **内容保护**: 确保文本内容不被数学规则修改

## 配置选项

```python
CONFIG = {
    'mode': 'normalize',                    # 运行模式
    'remove_trailing': False,               # 移除尾部命令
    'enable_synonym_replacement': False,    # 同义词替换
    'unify_environments': False,            # 环境统一
    'verbose': True,                        # 详细输出
    'show_input': True,                     # 显示输入
    'show_output': True,                    # 显示输出
    'show_comparison': False,               # 显示对比
}
```

## 测试验证

脚本经过全面测试验证：
- ✅ 用户原始问题（转义空格）已解决
- ✅ 命令冲突问题已解决
- ✅ 花括号匹配正确
- ✅ 复杂嵌套结构处理正确
- ✅ 边界情况处理完善

这个规范化流程确保了LaTeX公式的高质量标准化，为公式识别转换模型提供了一致、可靠的训练数据。
