# taken and modified from https://github.com/harvardnlp/im2markup
# tokenize latex formulas
import sys
import os
import re
import argparse
import subprocess
import shutil
from threading import Timer
from datetime import datetime


def run_cmd(cmd, timeout_sec=30):
    proc = subprocess.Popen(cmd, shell=True)
    kill_proc = lambda p: p.kill()
    timer = Timer(timeout_sec, kill_proc, [proc])
    try:
        timer.start()
        stdout,stderr = proc.communicate()
    finally:
        timer.cancel()
        
def tokenize_latex(latex_code, latex_type="", middle_file=""):
    """
    LaTeX规范化的主要入口函数
    数据流: 原始LaTeX字符串 → 环境统一 → JavaScript AST处理 → 规范化LaTeX

    Args:
        latex_code: 原始LaTeX字符串，如 "\\begin{align} x^2 + y^2 = z^2 \\end{align}"
        latex_type: LaTeX类型，"formula"或"tabular"
        middle_file: 中间文件名

    Returns:
        (success: bool, processed_latex: str)
    """
    # 步骤1: 输入验证 - 检查LaTeX代码是否为空
    if not latex_code:
        return False, latex_code

    # 步骤2: 类型检测 - 自动判断是公式还是表格
    if not latex_type:
        latex_type = "tabular" if "tabular" in latex_code else "formula"

    # 步骤3: 文件名生成 - 创建临时文件用于中间处理
    if not middle_file:
        middle_file = "out-" + datetime.now().strftime('%Y_%m_%d_%H_%M_%S') + ".txt"
    temp_file = middle_file + '.tmp'

    # 步骤4: 公式类型处理分支
    if latex_type == "formula":
        with open(temp_file, 'w') as f:
            prepre = latex_code
            # 步骤4.1: 环境统一 - 将多种对齐环境统一为aligned
            # 输入示例: "\\begin{split} x + y \\\\ = z \\end{split}"
            # 输出示例: "\\begin{aligned} x + y \\\\ = z \\end{aligned}"
            prepre = re.sub(r'\\begin{(split|align|alignedat|alignat|eqnarray)\*?}(.+?)\\end{\1\*?}', r'\\begin{aligned}\2\\end{aligned}', prepre, flags=re.S)

            # 步骤4.2: 矩阵环境统一 - 将smallmatrix统一为matrix
            # 输入示例: "\\begin{smallmatrix} a & b \\\\ c & d \\end{smallmatrix}"
            # 输出示例: "\\begin{matrix} a & b \\\\ c & d \\end{matrix}"
            prepre = re.sub(r'\\begin{(smallmatrix)\*?}(.+?)\\end{\1\*?}', r'\\begin{matrix}\2\\end{matrix}', prepre, flags=re.S)
            f.write(prepre)
    
        # 步骤5: 调用JavaScript AST处理器
        # 数据流: 预处理LaTeX → KaTeX解析 → AST标准化 → 规范化LaTeX
        cmd = r"cat %s | node %s %s > %s " % (temp_file, os.path.join(os.path.dirname(__file__), 'preprocess_formula.js'), 'normalize', middle_file)
        ret = subprocess.call(cmd, shell=True)
        os.remove(temp_file)

        # 步骤6: 错误处理 - 检查JavaScript处理是否成功
        if ret != 0:
            return False, latex_code

        # 步骤7: 操作符名称规范化
        # 将 \operatorname{sin} 形式转换为 \sin 形式
        operators = '\s?'.join('|'.join(['arccos', 'arcsin', 'arctan', 'arg', 'cos', 'cosh', 'cot', 'coth', 'csc', 'deg', 'det', 'dim', 'exp', 'gcd', 'hom', 'inf',
                                        'injlim', 'ker', 'lg', 'lim', 'liminf', 'limsup', 'ln', 'log', 'max', 'min', 'Pr', 'projlim', 'sec', 'sin', 'sinh', 'sup', 'tan', 'tanh']))
        ops = re.compile(r'\\operatorname {(%s)}' % operators)

        # 步骤8: 结果读取和token处理
        with open(middle_file, 'r') as fin:
            for line in fin:
                tokens = line.strip().split()  # 按空格分割tokens
                tokens_out = []
                for token in tokens:
                    tokens_out.append(token)  # 保留所有有效tokens
                post = ' '.join(tokens_out)  # 重新组合为字符串

                # 步骤8.1: 操作符名称替换
                # 输入示例: "\\operatorname { s i n }"
                # 输出示例: "\\sin"
                names = ['\\'+x.replace(' ', '') for x in re.findall(ops, post)]
                post = re.sub(ops, lambda match: str(names.pop(0)), post).replace(r'\\ \end{array}', r'\end{array}')
        os.remove(middle_file)

        # 步骤9: 返回处理结果
        # 输出格式: 完全规范化的LaTeX字符串
        return True, post
    
    elif latex_type == "tabular":
        latex_code = latex_code.replace("\\\\%", "\\\\ %")
        latex_code = latex_code.replace("\%", "<PERCENTAGE_TOKEN>")
        latex_code = latex_code.split('%')[0]
        latex_code = latex_code.replace("<PERCENTAGE_TOKEN>", "\%")
        if not "\\end{tabular}" in latex_code:
            latex_code += "\\end{tabular}"
        with open(middle_file, 'w') as f:
            f.write(latex_code.replace('\r', ' ').replace('\n', ' '))
        cmd = "perl -pe 's|hskip(.*?)(cm\\|in\\|pt\\|mm\\|em)|hspace{\\1\\2}|g' %s > %s"%(middle_file, temp_file)
        ret = subprocess.call(cmd, shell=True)
        if ret != 0:
            return False, latex_code
        os.remove(middle_file)
        cmd = r"cat %s | node %s %s > %s " % (temp_file, os.path.join(os.path.dirname(__file__), 'preprocess_tabular.js'), 'tokenize', middle_file)
        ret = subprocess.call(cmd, shell=True)
        os.remove(temp_file)
        if ret != 0:
            return False, latex_code
        with open(middle_file, 'r') as fin:
            for line in fin:
                tokens = line.strip().split()
                tokens_out = []
                for token in tokens:
                    tokens_out.append(token)
                post = ' '.join(tokens_out)
        os.remove(middle_file)
        return True, post
    else:
        print(f"latex type{latex_type} unrecognized.")
        return False, latex_code

if __name__ == '__main__':
    latex_code = open("2.txt", 'r').read().replace('\r', ' ')
    print("=>", latex_code)
    new_code = tokenize_latex(latex_code)
    print("=>", new_code)