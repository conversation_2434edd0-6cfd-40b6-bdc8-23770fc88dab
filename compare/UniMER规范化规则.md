# UniMERNet LaTeX编码统一化处理规则

## 概述

UniMERNet项目通过三个核心文件实现LaTeX编码的统一化处理：
1. `tokenize_latex.py` - Python入口，负责流程控制和初步处理
2. `preprocess_formula.js` - JavaScript核心处理，基于KaTeX进行AST解析和重构
3. `latex_processor.py` - Python深度处理，实现细粒度的规范化

## 环境层面统一化规则

### 数学环境标准化
```
split → aligned
align → aligned
alignedat → aligned
alignat → aligned
eqnarray → aligned
split* → aligned
align* → aligned
alignedat* → aligned
alignat* → aligned
eqnarray* → aligned
```

### 矩阵环境标准化
```
smallmatrix → matrix
smallmatrix* → matrix
```

### 特殊矩阵命令替换
```
\pmatrix → \mypmatrix
\matrix → \mymatrix
```

## 字体命令统一化规则

### 字体命令标准化
```
{\rm → \mathrm{
{ \rm → \mathrm{
\rm{ → \mathrm{
mbox → mathrm
hbox → mathrm
```

### 字体标记移除规则
```
\lowercase → (移除)
\uppercase → (移除)
\raggedright → (移除)
\arraybackslash → (移除)
```

## 结构层面统一化规则

### 分组和文本处理
```
所有分组 → { } 格式
所有文本块 → \mathrm { } 包裹
上下标非单一分组 → 自动添加大括号保护
```

### 分数和根号标准化
```
无横线分数 → \binom
有横线分数 → \frac
带指数根号 → \sqrt [ ] { }
普通根号 → \sqrt { }
```

### 定界符和重音符号
```
定界符 → \left 和 \right 前缀
重音符号 → 根据基础类型智能添加大括号
```

### 空白符号处理
```
\~ → (空格)
\> → (空格)
$ → (空格)
\label{...} → (移除)
\\ (非矩阵环境) → \,
```

### 注释处理
```
% 开头的行 → (移除注释部分)
```

## 操作符名称统一化规则

### 函数名标准化
```
\operatorname{函数名} → \函数名
```

涵盖的函数包括：
- 三角函数：`arccos`, `arcsin`, `arctan`, `cos`, `sin`, `tan`, `cot`, `sec`, `csc`
- 双曲函数：`cosh`, `sinh`, `tanh`, `coth`
- 对数函数：`log`, `ln`, `lg`
- 极值函数：`max`, `min`, `sup`, `inf`, `lim`, `liminf`, `limsup`
- 其他函数：`exp`, `arg`, `deg`, `det`, `dim`, `gcd`, `hom`, `ker`, `Pr`
- 专业函数：`injlim`, `projlim`
```
### 操作符修饰符处理
```
\operatorname* → \operatorname (移除星号)
```

## 符号层面统一化规则

### 连字符处理
```
-- → - -
--- → - - -
```

### 省略号统一化
```
… → . . .
\ldots → . . .
\hdots → . . .
\cdots → . . .
\dddot → . . .
\dots → . . .
\dotsc → . . .
\dotsi → . . .
\dotsm → . . .
\dotso → . . .
\dotsb → . . .
\mathellipsis → . . .
```

### 数学函数字符级展开
```
\ex → \mathrm { e x }
\ln → \mathrm { l n }
\lg → \mathrm { l g }
\cot → \mathrm { c o t }
\mod → \mathrm { m o d }
\bmod → \mathrm { m o d }
\pmod → \mathrm { m o d }
\min → \mathrm { m i n }
\max → \mathrm { m a x }
\ker → \mathrm { k e r }
\hom → \mathrm { h o m }
\sec → \mathrm { s e c }
\scs → \mathrm { s c s }
\csc → \mathrm { c s c }
\deg → \mathrm { d e g }
\arg → \mathrm { a r g }
\log → \mathrm { l o g }
\dim → \mathrm { d i m }
\exp → \mathrm { e x p }
\sin → \mathrm { s i n }
\cos → \mathrm { c o s }
\tan → \mathrm { t a n }
\tanh → \mathrm { t a n h }
\cosh → \mathrm { c o s h }
\sinh → \mathrm { s i n h }
\coth → \mathrm { c o t h }
\arcsin → \mathrm { a r c s i n }
\arccos → \mathrm { a r c c o s }
\arctan → \mathrm { a r c t a n }
```

## 命令合并和清理规则

### 字符串命令合并
```
\string \abc → \string\abc
```

### 大型运算符合并
```
\big ( → \big(
\big [ → \big[
\big { → \big{
\big | → \big|
\big \} → \big\}
\big ] → \big]
\big ) → \big)
```

同样规则适用于：`\Big`, `\bigg`, `\Bigg`, `\bigl`, `\bigr`, `\Bigl`, `\Bigr`, `\biggl`, `\biggr`, `\Biggl`, `\Biggr`, `\bigm`, `\Bigm`, `\biggm`, `\Biggm`

### 大型运算符与LaTeX命令合并
```
\big \langle → \big\langle
\Big \rangle → \Big\rangle
\bigg \{ → \bigg\{
\Bigg \| → \Bigg\|
```

### 有害命令移除
```
\lefteqn → 移除（会导致字符重叠）
\operatorname * → \operatorname（移除星号）
\footnote → ^
```

### 重音符号合并
```
\' e → \'e（仅当后续不是大括号时）
```

### 特殊符号替换
```
SSSSSS → $
 S S S S S S → $
```

## 布局和盒子命令处理

### 尺寸参数合并
```
[ -1.5ex ] → [-1.5ex]
[ 1.5pt ] → [1.5pt]
[ 3 mm ] → [3mm]
```

### 盒子命令合并
```
\parbox { 3cm } → \parbox{3cm}
\raisebox{-1.5ex}[0pt] → \raisebox{-1.5ex}[0pt]
{ \char'177} → {\char'177}
```

### 线条命令统一
```
\rule {1pt} {2pt} → \rule{1pt}{2pt}
\specialrule {1pt} {2pt} {2pt} → \specialrule{1pt}{2pt}{2pt}
```

## 颜色命令清理规则

移除所有颜色相关命令为后续颜色添加做准备：
```
\colorbox[RGB]{r,g,b} { content } → content
\color[rgb]{r,g,b} { content } → content
\textcolor[RGB]{r,g,b} { content } → content
\cellcolor[RGB]{r,g,b} { content } → content
\colorbox{colorname} { content } → content
\color{colorname} { content } → content
\textcolor{colorname} { content } → content
\cellcolor{colorname} { content } → content
```

## 智能括号补全规则

### 单参数token补全规则
为以下命令自动补全缺失的大括号：

**重音符号类：**
```
\widetilde, \overline, \hat, \widehat, \tilde, \Tilde, \dot, \bar, \vec, \underline, \underbrace, \check, \breve, \Bar, \Vec, \mathring, \ddot, \Ddot, \dddot, \ddddot
```

**字体命令类：**
```
\boldsymbol, \pmb, \textbf, \mathrm, \mathbf, \mathbb, \mathcal, \textmd, \texttt, \textnormal, \text, \textit, \textup, \mathop, \mathbin, \smash, \operatorname, \textrm, \mathfrak, \emph, \textsf, \textsc
```

### 双参数token补全规则
为以下命令确保两个参数都有大括号保护：
```
\frac, \binom, \overset, \underset, \stackrel
```

### 特殊参数token补全规则
为以下命令处理可选参数和必选参数的组合：
```
\sqrt[n]{x} → \sqrt[n]{x}
\sqrt{x} → \sqrt{x}
\xrightarrow[下标]{上标} → \xrightarrow[下标]{上标}
\xleftarrow[下标]{上标} → \xleftarrow[下标]{上标}
```

## 空间命令处理规则

### 公式环境中的空间命令
```
\hspace { 1.5 cm } → \hspace{1.5cm}
\vspace { 2 pt } → \vspace{2pt}
```

### 表格环境中的空间命令
```
\hspace → 完全移除
\vspace → 完全移除
```

## 环境命令格式化规则

```
\begin {tabular} → \begin{tabular}
\end {tabular} → \end{tabular}
\begin {array} → \begin{array}
\end {array} → \end{array}
\begin{array} { l r c } → \begin{array} {lrc}
```

## 复杂命令合并规则

使用括号匹配算法处理以下复杂命令：
```
\cmidrule ( l { 3 p t } r { 3 p t } ) { 1 - 1 } → 合并为单一token
\cline { 1 - 3 } → 合并为单一token
```


## AST节点处理规则

### 基础节点类型
```
mathord → 数学符号处理（mathrm字体时字符间添加空格）
textord → 文本符号处理
bin → 二元操作符处理
rel → 关系操作符处理
open → 开括号处理
close → 闭括号处理
inner → 内部间距处理
punct → 标点符号处理
```

### 复杂结构处理
```
ordgroup → { 内容 }
text → \mathrm { 内容 }
supsub → base _ { sub } ^ { sup }（非ordgroup时添加大括号）
genfrac → \frac 分子 分母 或 \binom 分子 分母
array → \begin{array} { 对齐 } 内容 \end{array}
sqrt → \sqrt [ 指数 ] 内容 或 \sqrt 内容
```

### 高级功能处理
```
leftright → \left左括号 内容 \right右括号
accent → 重音符号 { 内容 } 或 重音符号 内容（根据base类型）
spacing → ~ 或 间距值
op → \operatorname { 内容 } 或 \operatorname* { 内容 }
font → \字体类型 内容（mbox/hbox → mathrm）
delimsizing → 函数名 值
styling → 原始样式 内容
sizing → \mathrm { 内容 } 或 原始大小 内容（\rm特殊处理）
```

### 装饰功能处理
```
overline → \overline { 内容 }
underline → \underline { 内容 }
rule → \rule { 宽度 } { 高度 }
llap → \llap 内容
rlap → \rlap 内容
phantom → \phantom { 内容 }
```

## 数组环境特殊处理规则

### 空单元格清理
```
{ } → (清理空的单元格内容)
```

### 行有效性检查
```
row.some(cell => cell.value.length > 0) → 只处理非空行
```

### 列对齐默认值
```
如果没有指定cols → 默认使用'l'对齐
```

## 技术实现特点

1. **多阶段处理架构**：从粗粒度环境统一到细粒度符号处理的递进式优化
2. **AST驱动的核心处理**：利用KaTeX成熟的解析器确保语法正确性和结构完整性
3. **智能算法集成**：括号匹配、深度计数、递归处理等复杂算法确保嵌套结构的正确处理
4. **全面的容错机制**：异常情况下返回原始输入，确保系统稳定性和数据完整性
5. **模块化设计理念**：Python和JavaScript各自发挥优势，形成高效的协作处理链
6. **颜色系统准备**：为后续的视觉匹配和边界框生成做好充分准备
7. **字符级处理支持**：通过函数名展开等技术支持精确的字符级匹配和评估
8. **环境自适应处理**：根据公式环境和表格环境采用不同的处理策略

这套完整的统一化系统确保了不同来源、不同写法的LaTeX代码能够转换为高度标准化、一致性的形式，为机器学习模型的训练、推理和评估提供了可靠的数据基础，是公式识别和处理领域的重要技术贡献。