# LaTeX规范化脚本流程图与函数说明

## 概述

LaTeX规范化是图片转LaTeX码模型训练的关键预处理步骤，本脚本采用Python + JavaScript混合架构，通过AST解析和规则处理实现LaTeX代码的标准化。

## 整体架构

```
输入LaTeX字符串
    ↓
Python主控制器 (normalize_optimized.py)
    ↓
JavaScript AST处理器 (preprocess_formula.js)
    ↓
Python规则处理器 (normalize_optimized.py)
    ↓
输出规范化LaTeX字符串
```

## 详细流程图

### 1. 主控制流程

```mermaid
graph TD
    A[输入LaTeX字符串] --> B[validate_config]
    B --> C{配置验证}
    C -->|失败| D[输出错误信息]
    C -->|成功| E[process_latex]
    
    E --> F{运行模式}
    F -->|tokenize| G[tokenize_latex_string]
    F -->|normalize| H[完整规范化流程]
    
    G --> I[replace_synonym_tokens]
    I --> J[输出tokenize结果]
    
    H --> K[JavaScript AST处理]
    K --> L[replace_synonym_tokens]
    L --> M[normalize_latex]
    M --> N[输出规范化结果]
```

### 2. JavaScript AST处理流程

```mermaid
graph TD
    A[LaTeX字符串输入] --> B[注释处理]
    B --> C[特殊字符预处理]
    C --> D[清理LaTeX命令]
    D --> E{是否结构化环境}
    E -->|否| F[换行符处理]
    E -->|是| G[保持换行符]
    F --> H[模式选择]
    G --> H
    H --> I{处理模式}
    I -->|tokenize| J[仅解析tokens]
    I -->|normalize| K[完整AST处理]
    
    K --> L[\rm命令统一化]
    L --> M[KaTeX AST解析]
    M --> N[AST渲染]
    N --> O[后处理恢复$符号]
    O --> P[输出结果]
    
    J --> Q[输出tokenized结果]
```

### 3. Python规则处理流程

```mermaid
graph TD
    A[tokenized LaTeX] --> B[保护text命令]
    B --> C{remove_trailing}
    C -->|是| D[remove_trailing_latex]
    C -->|否| E[基础命令替换]
    D --> E
    
    E --> F[移除对齐命令]
    F --> G[移除大小写转换]
    G --> H[Token合并处理]
    H --> I[颜色命令移除]
    I --> J[括号补全处理]
    J --> K[unified_math_function_processing]
    K --> L[unified_space_processing]
    L --> M[连字符展开]
    M --> N[省略号统一化]
    N --> O[remove_empty_braces]
    O --> P[remove_redundant_braces]
    P --> Q[fix_align_environment_braces]
    Q --> R[恢复text命令]
    R --> S[输出规范化结果]
```

## 核心函数详细说明

### Python脚本函数 (normalize_optimized.py)

#### 配置与验证函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `validate_config()` | 验证配置参数有效性 | CONFIG字典 | bool | 检查必需参数和参数类型 |
| `process_latex()` | 主处理流程控制 | 无 | 规范化结果 | 根据模式选择处理路径 |

#### 核心处理函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `normalize_latex_string()` | 完整规范化入口 | LaTeX字符串 | 规范化字符串 | 协调JavaScript和Python处理 |
| `tokenize_latex_string()` | tokenize处理入口 | LaTeX字符串 | tokenized字符串 | 调用JavaScript AST处理 |
| `normalize_latex()` | Python规则处理主函数 | LaTeX字符串, rm_trail | 规范化字符串 | 执行所有Python规则处理 |

#### JavaScript调用函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `tokenize_latex()` | 调用JavaScript处理器 | LaTeX字符串 | (success, processed) | 通过subprocess调用Node.js |
| `protect_text_command()` | 保护text命令 | 匹配对象 | 占位符 | 防止text内容被修改 |

#### 通用处理函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `merge_tokens_with_pattern()` | 通用token合并 | text, pattern, process_func | 处理后的文本 | 抽象重复的token合并逻辑 |
| `latex_aware_tokenizer()` | LaTeX语法感知tokenizer | LaTeX字符串 | token列表 | 状态机方法正确理解LaTeX结构 |

#### 空格处理函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `unified_space_processing()` | 统一空格处理 | LaTeX字符串 | 处理后的字符串 | 先清理后添加，避免冲突 |
| `clean_latex_spaces()` | 清理不必要空格 | LaTeX字符串 | 清理后的字符串 | 移除多余空格 |
| `add_required_spaces()` | 添加必要空格 | LaTeX字符串 | 添加空格后的字符串 | 为命令和符号间添加分隔 |

#### 数学函数处理

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `unified_math_function_processing()` | 统一数学函数处理 | LaTeX字符串 | 处理后的字符串 | 将operatorname和直接函数名转换为\mathrm{}格式 |

#### 括号处理函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `remove_empty_braces()` | 删除空花括号 | LaTeX字符串 | 处理后的字符串 | 删除{}但保留有意义的空格 |
| `remove_redundant_braces()` | 删除多余嵌套花括号 | LaTeX字符串 | 处理后的字符串 | 处理{{content}}→{content} |
| `fix_align_environment_braces()` | 修复align环境花括号 | LaTeX字符串 | 处理后的字符串 | 专门处理align环境中的花括号问题 |

#### 辅助函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `find_matching_brace()` | 查找匹配括号 | sequence, start_index, brace | 匹配括号索引 | 用于括号补全处理 |
| `remove_trailing_latex()` | 移除尾部LaTeX命令 | LaTeX字符串 | 清理后的字符串 | 移除间距和装饰命令 |

#### 同义词替换函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `replace_synonym_tokens()` | 同义词token替换 | tokenized字符串 | 替换后的字符串 | 根据CSV配置文件替换同义词 |

### JavaScript脚本函数 (preprocess_formula.js)

#### 主处理函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `rl.on('line')` | 主处理入口 | 标准输入行 | 标准输出 | 处理单行LaTeX输入 |

#### AST渲染函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `buildExpression()` | 表达式构建 | AST节点数组, options | 无(更新norm_str) | 遍历AST节点，调用相应处理函数 |
| `buildGroup()` | 单个节点处理 | AST节点, options | 无(更新norm_str) | 根据节点类型分发到对应处理函数 |

#### 节点类型处理函数 (groupTypes)

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `groupTypes.mathord` | 数学普通字符 | 字符AST节点 | 规范化字符序列 | 处理变量、数字等 |
| `groupTypes.textord` | 文本字符 | 文本AST节点 | 规范化文本 | 处理普通文本字符 |
| `groupTypes.bin` | 二元运算符 | 运算符AST节点 | 运算符+空格 | 处理+, -, *, 等 |
| `groupTypes.rel` | 关系运算符 | 关系符AST节点 | 关系符+空格 | 处理=, <, >, 等 |
| `groupTypes.open/close` | 括号处理 | 括号AST节点 | 括号+空格 | 处理各种括号 |
| `groupTypes.inner` | 内部元素 | 内部元素AST节点 | 元素值+空格 | 处理内部元素 |
| `groupTypes.punct` | 标点符号 | 标点AST节点 | 标点+空格 | 处理标点符号 |

#### 复杂结构处理函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `groupTypes.ordgroup` | 有序组处理 | 组AST节点 | {子表达式...} | 处理用{}包围的表达式组 |
| `groupTypes.text` | 文本模式处理 | 文本AST节点 | \text{文本内容} | 处理文本模式内容 |
| `groupTypes.supsub` | 上下标处理 | 上下标AST节点 | base_sub^sup | 处理上标和下标 |
| `groupTypes.genfrac` | 分数处理 | 分数AST节点 | \frac{分子}{分母} | 处理分数和二项式系数 |
| `groupTypes.array` | 数组/矩阵处理 | 数组AST节点 | \begin{环境}...\end{环境} | 处理矩阵、数组等结构化环境 |
| `groupTypes.sqrt` | 根号处理 | 根号AST节点 | \sqrt[n]{expr} | 处理平方根和n次根 |
| `groupTypes.leftright` | 定界符处理 | 定界符AST节点 | \left(内容\right) | 处理自动调整大小的括号 |

#### 特殊符号处理函数

| 函数名 | 作用 | 输入 | 输出 | 说明 |
|--------|------|------|------|------|
| `groupTypes.accent` | 重音符号处理 | 重音AST节点 | \hat{x} | 处理帽子、波浪线等重音 |
| `groupTypes.spacing` | 间距处理 | 间距AST节点 | 标准化间距命令 | 处理空格和各种间距命令 |
| `groupTypes.op` | 操作符处理 | 操作符AST节点 | 符号或\operatorname | 处理求和、积分等大型操作符 |
| `groupTypes.font` | 字体处理 | 字体AST节点 | \mathbf{...} | 处理数学字体命令 |
| `groupTypes.delimsizing` | 定界符大小 | 大小AST节点 | \big( | 处理\big, \Big等大小调整 |
| `groupTypes.overline/underline` | 划线处理 | 划线AST节点 | \overline{expr} | 处理上划线和下划线 |

## 数据流说明

### 1. 输入处理阶段
- **输入验证**: 检查LaTeX字符串是否为空或无效
- **模式选择**: 根据配置选择tokenize或normalize模式
- **预处理**: 保护特殊命令，准备JavaScript处理

### 2. JavaScript AST处理阶段
- **KaTeX解析**: 将LaTeX字符串解析为抽象语法树
- **AST遍历**: 递归遍历AST节点，应用规范化规则
- **渲染输出**: 将处理后的AST重新渲染为LaTeX字符串

### 3. Python规则处理阶段
- **token合并**: 合并分散的token，统一格式
- **空格处理**: 清理和添加必要的空格
- **数学函数统一**: 将各种数学函数格式统一为\mathrm{}格式
- **括号处理**: 删除空括号，修复嵌套括号
- **环境修复**: 专门处理align等特殊环境

### 4. 后处理阶段
- **同义词替换**: 根据配置文件替换同义词token
- **最终清理**: 移除尾部命令，清理多余空格
- **结果输出**: 输出最终规范化的LaTeX字符串

## 关键优化点

1. **避免冲突**: 通过先清理后添加的策略避免空格处理冲突
2. **保护机制**: 保护\text{}等特殊命令不被错误修改
3. **状态机tokenizer**: 正确理解LaTeX语法结构，避免错误分割
4. **通用函数**: 抽象重复逻辑，提高代码复用性
5. **错误处理**: 完善的异常处理机制，确保处理失败时返回原始字符串

## 配置说明

```python
CONFIG = {
    'mode': 'normalize',  # 处理模式: 'tokenize' 或 'normalize'
    'latex_string': '...',  # 待处理的LaTeX字符串
    'remove_trailing': False,  # 是否移除尾部LaTeX命令
    'enable_synonym_replacement': False,  # 是否启用同义词替换
    'unify_environments': False,  # 是否统一对齐环境
}
```

这个架构设计确保了LaTeX规范化处理的鲁棒性和可扩展性，通过Python和JavaScript的协同工作，实现了高质量的LaTeX代码标准化。 