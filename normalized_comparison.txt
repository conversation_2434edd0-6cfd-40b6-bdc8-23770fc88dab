G = K \cdot H = K ( k _ { m } ) \cdot H ( h _ { s } ) , | G=K \cdot H=K(k_m) \cdot H(h_s),
\ln P ( E ) - \ln D ( E ) | \mathrm{ln} P(E)- \mathrm{ln} D(E)
x = - \sqrt { y - 3 } | x=- \sqrt { y- } 3
\begin{align*}\int_{\partial\Omega}\omega(\overline{e}^{\prime}(\overline{h_{t}})+\varphi_{t})d\mathcal{H}^{d-1}=0.\end{align*} | \begin {aligned} \int _ \partial \Omega \omega ( \overline { {e} } ^ \prime ( \overline { {h_t} } ) + \varphi _t) d \mathcal { H^d- } 1 = 0 . \end {aligned}
7 6 0 | 7 6 0
\lnapprox | \lnapprox
\begin{array} { r l } & { E _ { 0 } ( \boldsymbol { \rho } , z ) = g _ { 1 2 } ^ { d } n _ { 0 } \Omega ( \boldsymbol { \rho } , z ) , \qquad \mathrm { w h e r e } \qquad \Omega ( \boldsymbol { \rho } , z ) = \int d q \int d u f ( q , u , \boldsymbol { \rho } , z ) , } \\ & { f ( q , u , \boldsymbol { \rho } , z ) = \frac { 1 } { 4 \sqrt { \pi } } q ^ { 2 } \left[ \frac { 3 u ^ { 2 } } { \Lambda ^ { 2 } \left( 1 - u ^ { 2 } \right) + u ^ { 2 } } - 1 \right] J _ { 0 } \left[ q \sqrt { 1 - u ^ { 2 } } \tilde { \boldsymbol { \rho } } \right] e ^ { - \frac { 1 } { 4 } q ^ { 2 } - j q u \tilde { z } } . } \end{array} | \begin {array} {r l} & E_ 0 ( \boldsymbol { \rho } ,z)=g_ 1 2 ^d n_ 0 \Omega ( \boldsymbol { \rho } ,z), \qquad \mathrm { w } h e r e \qquad \Omega ( \boldsymbol { \rho } ,z)= \int d q \int d u f(q,u, \boldsymbol { \rho } ,z), \\ & f(q,u, \boldsymbol { \rho } ,z)= \frac {1} {4 \sqrt \pi} q^ 2 \left [\frac{3 u^2}{\Lambda^2 \left(1-u^2 \right)+u^2}-1 \right] J_ 0 \left [q \sqrt 1-u^2 \tilde \boldsymbol \rho \right] e^- \frac {1} {4} q^ 2 -j q u \tilde { z. } \end {array}
\begin{align*}\left.\frac{d}{d\tau}\right|_{\tau=0}x^{M}(g g_{A}(\tau))\partial_{M}|_{g}=L^{M}_{A}(g)\partial_{M}|_{g}\,.\end{align*} | \begin {aligned} \left . \frac {d} {d \tau} \right |_ \tau = 0 x^M(g g_A( \tau ) ) \partial _M |_g=L_A^M(g) \partial _M |_g \, . \end {aligned}
\begin{array} { r l } { q _ { x } ^ { \prime } } & { { } = q _ { x } , } \\ { q _ { y } ^ { \prime } } & { { } = q _ { y } , } \\ { p _ { x } ^ { \prime } } & { { } = p _ { x } + 4 \, c _ { 2 0 } \cos ( \theta ) \, q _ { x } , } \\ { p _ { y } ^ { \prime } } & { { } = p _ { y } + 4 \, c _ { 0 2 } \sec ( \theta ) \, q _ { y } . } \end{array} | \begin {array} {r l} q_x^ \prime &=q_x, \\ q_y^ \prime &=q_y, \\ p_x^ \prime &=p_x+ 4 \, c_ 2 0 \mathrm{cos} ( \theta ) \, q_x, \\ p_y^ \prime &=p_y+ 4 \, c_ 0 2 \mathrm{sec} ( \theta ) \, q_y. \end {array}
\beta = \frac { \alpha \theta } { c _ { p r } } \frac { \partial \mu _ { r } } { \partial \theta } \qquad \rightarrow \qquad \frac { \partial \mu _ { r } } { \partial \theta } = \frac { c _ { p r } \beta } { \alpha \theta } \qquad \rightarrow \qquad \mu _ { r } = \frac { c _ { p r } \beta } { \alpha } \ln { \frac { \theta } { \theta _ { \star } } } + \mu _ { 0 } ( S ) | \beta = \frac {\alpha \theta} {c_p r} \frac {\partial \mu_r} {\partial \theta} \qquad \rightarrow \qquad \frac {\partial \mu_r} {\partial \theta} = \frac {c_p r \beta} {\alpha \theta} \qquad \rightarrow \qquad \mu _r= \frac {c_p r \beta} {\alpha} \mathrm{ln} \frac {\theta} {\theta_\star} + \mu _ 0 ( S)
\begin{align*}\mu=\eta+\alpha\delta_u\quad\mbox{and}\nu=\eta+\alpha\delta_v.\end{align*} | \begin {aligned} \mu = \eta + \alpha \delta _ {u} \quad \text {and} \nu = \eta + \alpha \delta _ {v} . \end {aligned}
\begin{array} { r } { \mathbb { A } = \left( \begin{array} { l l l l l } { R _ { 0 } } & { } & { } & { } & { } \\ { - \Tilde { Q } _ { 1 } } & { R _ { 1 } } & { } & { } & { } \\ { P _ { 2 } } & { - \Tilde { Q } _ { 2 } } & { R _ { 2 } } & { } \\ { 0 } & { P _ { 3 } } & { - \Tilde { Q } _ { 3 } } & { R _ { 3 } } & { } \\ { \vdots } & { } & { } & { } & { \ddots } \end{array} \right) } \end{array} | \begin {array} {r} \mathbb { A= } \left ( \begin {array} {l l l l l} R_ 0 & & & & \\ - \Tilde { Q_ } 1 & R_ 1 & & & \\ P_ 2 &- \Tilde { Q_ } 2 & R_ 2 & \\ 0 & P_ 3 &- \Tilde { Q_ } 3 & R_ 3 & \\ \vdots & & & & \ddots \end {array} \right ) \end {array}
\begin{gather*}\left(\begin{array}{c}u_{0n}\\u_{1n}\end{array}\right)\to \left(\begin{array}{c}\phi u_0\\\phi u_1\end{array}\right)=\left(\begin{array}{c}u_0\\u_1\end{array}\right)\ \mathcal{H}\end{gather*} | \begin {gather*} \left ( \begin {array} {c} u_ {0n} \\ u_ {1n} \end {array} \right ) \to \left ( \begin {array} {c} \phi u_ 0 \\ \phi u_ 1 \end {array} \right ) = \left ( \begin {array} {c} u_ 0 \\ u_ 1 \end {array} \right ) \ \ mathcal {H} \end {gather*}
( \gamma _ { \kappa } \partial ^ { \kappa } + M ) \psi ^ { \mu } ( x ) = 0 . | ( \gamma _ \kappa \partial ^ \kappa +M) \psi ^ \mu ( x)= 0 .
{ \cal L } _ { Y u k } ^ { Q } = \lambda _ { L } H _ { 1 } \bar { Q } _ { L } Q _ { L } ^ { c } + \lambda _ { R } H _ { 1 } \bar { Q } _ { R } Q _ { R } ^ { c } + 2 D \bar { Q } _ { L } Q _ { R } + \mathrm { H . c . } , | \cal L_Y u k^Q= \lambda _L H_ 1 \bar { Q_L } Q_L^c+ \lambda _R H_ 1 \bar { Q_R } Q_R^c+ 2 D \bar { Q_L } Q_R+ \mathrm { H.c., }
\gamma _ { j i } ^ { c } = \left\{ \begin{array} { c c l } { \displaystyle \frac { g _ { \varepsilon } ( n _ { j } + 1 ) - g _ { \varepsilon } ( n _ { i } + 1 ) } { \frac { 1 } { n _ { i } + 1 } - \frac { 1 } { n _ { j } + 1 } } } & { \mathrm { ~ i f ~ } } & { n _ { j } \not = n _ { i } , } \\ { \left[ n _ { i } \right] _ { + } + 1 } & { \mathrm { ~ i f ~ } } & { n _ { j } = n _ { i } . } \end{array} \right. | \gamma _j i^c= \left \{ \begin {array} {c c l} \displaystyle \frac {g_\varepsilon(n_j+1)-g_\varepsilon(n_i+1)} {\frac{1}{n_i+1}-\frac{1}{n_j+1}} & \mathrm { ~ } i f ~ & n_j \not =n_i, \\ \left [n_i \right] _++ 1 & \mathrm { ~ } i f ~ & n_j=n_i. \end {array} \right .
R e \biggl ( \frac { \varepsilon ^ { \prime } } { \varepsilon } \biggr ) = ( 2 3 \pm 7 ) \times 1 0 ^ { - 4 } , \, \, \, \, \, \, | R e \biggl ( \frac {\varepsilon^\prime} {\varepsilon} \biggr ) =( 2 3 \pm 7 ) \times 1 0 ^- 4 , \, \, \, \, \, \,
\begin{align*} A_r^s(x_1, \dots, x_r) := \begin{pmatrix} e_{r, 1}^0 & e_{r, 2}^0 & \cdots & e_{r, r}^0 \\ e_{r, 1}^1 & e_{r, 2}^1 & \cdots & e_{r, r}^1 \\ \vdots & \vdots & \ddots & \vdots \\ e_{r, 1}^{s - 1} & e_{r, 2}^{s - 1} & \cdots & e_{r, r}^{s - 1} \end{pmatrix}. \end{align*} | \begin {align*} A_r^s(x_ 1 , . . . ,x_r):= \begin {pmatrix} e_ {r,1} ^ 0 & e_ {r,2} ^ 0 & . . . & e_ {r,r} ^ 0 \\ e_ {r,1} ^ 1 & e_ {r,2} ^ 1 & . . . & e_ {r,r} ^ 1 \\ \vdots & \vdots & \ddots & \vdots \\ e_ {r,1} ^ {s-1} & e_ {r,2} ^ {s-1} & . . . & e_ {r,r} ^ {s-1} \end {pmatrix} . \end {align*}
t \geq T | t \geq T
\hat { \mu } ( \sigma ) = \frac { \tau _ { v } } { \tau _ { e } } \left[ 1 - \mathrm { i } ( \sigma \tau _ { e } ) ^ { - 1 } + ( \mathrm { i } \sigma \tau _ { a } ) ^ { - \alpha } \Gamma ( 1 + \alpha ) \right] ^ { - 1 } \ , | \hat { \mu } ( \sigma ) = \frac {\tau_v} {\tau_e} \left [1-\mathrm i(\sigma \tau_e)^-1+(\mathrm i \sigma \tau_a)^-\alpha \Gamma(1+\alpha) \right] ^- 1 \,
\theta = \pi / 6 , \ \sigma _ { 1 } / x _ { 0 } ^ { - } = 0 . 2 5 , \ \sigma _ { 2 } / x _ { 0 } ^ { - } = 0 . 3 5 | \theta = \pi / 6 , \ \ sigma_ 1 /x_ 0 ^-= 0 . 2 5 , \ \ sigma_ 2 /x_ 0 ^-= 0 . 3 5
T = 3 7 2 | T= 3 7 2
\int _ { 0 } ^ { \infty } \frac { ( 1 + \cosh t \cos \psi _ { 1 } ) ( \cosh \frac { 1 } { 2 } t + \cos \frac { 1 } { 2 } \psi _ { 2 } - \sinh t \sin \psi _ { 1 } \sinh \frac { 1 } { 2 } t \sin \frac { 1 } { 2 } \psi _ { 2 } ) } { ( \cosh t + \cos \psi _ { 1 } ) ^ { 2 } ( \cosh t + \cos \psi _ { 2 } ) } \mathrm { e } ^ { \mathrm { i } \kappa _ { 0 } r _ { i } \cosh t } \mathrm { d } \, t . | \int _ 0 ^ \infty \frac {(1+\cosh t \mathrm{cos} \psi_1)(\cosh \frac{1}{2} t+\cos \frac{1}{2} \psi_2-\sinh t \mathrm{sin} \psi_1 \mathrm{sinh} \frac{1}{2} t \mathrm{sin} \frac{1}{2} \psi_2)} {(\cosh t+\cos \psi_1)^2(\cosh t+\cos \psi_2)} \mathrm { e^ } \mathrm { i } \kappa _ 0 r_i \mathrm{cosh} t \mathrm { d } \, t.
\begin{align*}\int_M \kappa_\Gamma \, \omega = \int_M \gamma^{*} \sigma = \operatorname{deg}(\gamma) \int_{\mathbb{S}^n} \sigma = c_n \operatorname{deg}(\gamma).\end{align*} | \begin {aligned} \int _ {M} \kappa _ {\Gamma} \, \omega = \int _ {M} \gamma ^* \sigma = \operatorname { d } e g( \gamma ) \int _ \mathbb { S^ } {n} \sigma =c_ {n} \operatorname { d } e g( \gamma ) . \end {aligned}
E _ { \varepsilon } \left( \Omega , X \right) = 4 \pi + \frac { \gamma \varepsilon ^ { 3 } } { 2 } ( 1 + \varepsilon + \varepsilon ^ { 2 } ) - ( 4 \pi \alpha ^ { 2 } \varepsilon ^ { 2 } - \frac { \gamma \alpha \varepsilon ^ { 4 } } { 2 } ) \log { ( \alpha \varepsilon ^ { 2 } ) } + \ensuremath { \operatorname { O } \left( \gamma \varepsilon ^ { 6 } , ( \gamma + 1 ) \alpha \varepsilon ^ { 4 } , \alpha ^ { 2 } \varepsilon ^ { 2 } , \alpha h ^ { 2 } \right) } . | E_ \varepsilon \left ( \Omega ,X \right ) = 4 \pi + \frac {\gamma \varepsilon^3} {2} ( 1 + \varepsilon + \varepsilon ^ 2 ) -( 4 \pi \alpha ^ 2 \varepsilon ^ 2 - \frac {\gamma \alpha \varepsilon^4} {2} ) \mathrm{log} ( \alpha \varepsilon ^ 2 ) + \ensuremath \operatorname { O } \left ( \gamma \varepsilon ^ 6 ,( \gamma + 1 ) \alpha \varepsilon ^ 4 , \alpha ^ 2 \varepsilon ^ 2 , \alpha h^ 2 \right ) .
\begin{align*}\widetilde{\cal H}_{\scriptscriptstyle [K,L]_{FN}}=\{([K,L]_{\scriptscriptstyle FN})^{\wedge},Q\}=-\{\widetilde{\cal H}_{\scriptscriptstyle K},\widetilde{\cal H}_{\scriptscriptstyle L}\}\end{align*} | \begin {aligned} \widetilde { \cal } H_ \sc riptscriptstyle [K,L] _F N= \{ ( [K,L] _ \sc riptscriptstyle F N)^ \wedge ,Q \} =- \{ \widetilde { \cal } H_ \sc riptscriptstyle K, \widetilde { \cal } H_ \sc riptscriptstyle L \} \end {aligned}
N _ { y } | N_y
T _ { 0 } ^ { \mu \nu } = - \left( \frac { g _ { s t r } \alpha ^ { \prime } } { 2 } \right) \prod _ { u } \frac { \delta ( \sqrt { \alpha ^ { \prime } } k _ { u } ) } { \sqrt { \tau _ { 2 } } } \frac { \eta ^ { \mu \nu } } { 2 \pi \alpha ^ { \prime } } \left( \prod _ { o } \frac { \delta ( \sqrt { \alpha ^ { \prime } } k _ { o } ) } { \sqrt { \tau _ { 2 } } } X _ { 1 , 1 } + 2 ^ { - ( d + 1 ) } X _ { 1 , 0 } \right) | T_ 0 ^ \mu \nu =- \left ( \frac {g_s t r \alpha^\prime} {2} \right ) \prod _u \frac {\delta(\sqrt \alpha^\prime k_u)} {\sqrt \tau_2} \frac {\eta^\mu \nu} {2 \pi \alpha^\prime} \left ( \prod _o \frac {\delta(\sqrt \alpha^\prime k_o)} {\sqrt \tau_2} X_ 1 , 1 + 2 ^-(d+ 1 ) X_ 1 , 0 \right )
\boldsymbol { u } = \Lambda r \exp \left( - \frac { r ^ { 2 } + z ^ { 2 } } { \delta ^ { 2 } } \right) \boldsymbol { e } _ { \theta } , | \boldsymbol { u= } \Lambda r \mathrm{exp} \left ( - \frac {r^2+z^2} {\delta^2} \right ) \boldsymbol { e_ } \theta ,
\begin{align*}\Delta{\cal D}^{\rm mon.}\left(x^2\right)=-4\pi^2\left<\rho({\bf x})\rho({\bf 0})\right>_\rho,\end{align*} | \begin {aligned} \Delta \cal D^ {\mathrm m o n.} \left ( x^ {2} \right ) =- 4 \pi ^ {2} \left < \rho ( \bf x) \rho ( \bf 0 ) \right >_ {\rho} , \end {aligned}
\begin{align*}\Pi^{\perp i}=-\frac{1}{\gamma} F^{\perp i} + \frac{k}{8\pi} \epsilon^{\perp ij}A_j\,.\end{align*} | \begin {aligned} \Pi ^ \perp i=- \frac {1} {\gamma} F^ \perp i+ \frac {k} {8 \pi} \epsilon ^ \perp i j A_ {j} \, . \end {aligned}
h ^ { 2 } > \frac { 1 6 } 9 \vec { h } ^ { 2 } | h^ 2 > \frac {1 6} {9} \vec { h^ } 2
- \frac { 2 g \beta ( g ) } { g ^ { 2 } } Y _ { i } a _ { 1 } L ^ { 2 } f _ { 0 } ( W _ { 1 L } , g ^ { 2 } ) | - \frac {2 g \beta(g)} {g^2} Y_i a_ 1 L^ 2 f_ 0 ( W_ 1 L,g^ 2 )
[ Y _ { + } ( z , \tilde { t } = 1 0 ^ { 4 } ) - H ( z , \tilde { t } = 1 0 ^ { 4 } ) ] B \tilde { t } | [Y_+(z,\tilde t=1 0^4)-H(z,\tilde t=1 0^4)] B \tilde { t }
( i , j , k ) , \: ( i _ { i n t } , j , k ) , \: ( i , j _ { i n t } , k ) , \: ( i , j , k _ { i n t } ) , \: ( i _ { e x t } , j , k ) , \: ( i , j _ { e x t } , k ) , \: ( i , j , k _ { e x t } ) . | ( i,j,k), \: ( i_i n t,j,k), \: ( i,j_i n t,k), \: ( i,j,k_i n t), \: ( i_e x t,j,k), \: ( i,j_e x t,k), \: ( i,j,k_e x t).
| f \rangle | | f \rangle
\begin{align*} \phi_j(x, E, {\bf g})= q^j \phi(q^{-j} x , \Omega^{3j} E,{\bf g})\end{align*} | \begin {aligned} \phi _ {j} ( x,E, \bf g)=q^ {j} \phi ( q^-j x, \Omega ^ 3 j E, \bf g) \end {aligned}
\begin{align*} {\cal O}_{\ell s} = j^{\{\mu_1\cdots\mu_s\}}\;\phi^\delta \;.\end{align*} | \begin {aligned} \cal O_ \ell s=j^ \{ \mu _ {1} . . . \mu _ {s} \} \; \phi ^ {\delta} \; . \end {aligned}
\begin{align*}\Upsilon _{1}=\left\{ i=2,\ldots,s_{n}-1:4\,||K^{2}||\,R_{n}(I_{i,n},E_{n})\geq p_{i,n}\,\sigma ^{2}\right\}, \end{align*} | \begin {aligned} \Upsilon _ 1 = \left \{ i= 2 , . . . ,s_n- 1 : 4 \, | | K^ 2 | | \, R_n(I_i,n,E_n) \geq p_i,n \, \sigma ^ 2 \right \} , \end {aligned}
\sigma ^ { 2 } ( t ) = \sum _ { n } n ^ { 2 } \{ | u _ { n } ( t ) | ^ { 2 } + | v _ { n } ( t ) | ^ { 2 } \} | \sigma ^ 2 ( t)= \sum _n n^ 2 \{ | u_n(t) |^ 2 +| v_n(t) |^ 2 \}
a ^ { \mu } ( x , \tau ) = \sum _ { s = \mathrm { p o l a r i z a t i o n s } } \int \frac { d ^ { 4 } k } { 2 \kappa } \left[ \varepsilon _ { s } ^ { \mu } a ( k , s ) e ^ { i ( k \cdot x + \sigma \kappa \tau ) / \hbar } + \varepsilon _ { s } ^ { \mu * } a ^ { * } ( k , s ) e ^ { - i ( k \cdot x + \sigma \kappa \tau ) / \hbar } \right] | a^ \mu ( x, \tau ) = \sum _s= \mathrm { p } o l a r i z a t i o n s \int \frac {d^4 k} {2 \kappa} \left [\varepsilon_s^\mu a(k,s) e^i(k \cdot x+\sigma \kappa \tau)/\hbar+\varepsilon_s^\mu*a^*(k,s) e^-i(k \cdot x+\sigma \kappa \tau)/\hbar \right]
\begin{align*}E[X^n] = \int_{-\infty}^\infty x^n f(x) dx,\,\,(n \geq 1).\end{align*} | \begin {aligned} E [X^{n}] = \int _- \infty ^ {\infty} x^ {n} f(x) d x, \, \, ( n \geq 1 ) . \end {aligned}
J ^ { \mu } ( \tau ) = \sum _ { i = 1 } ^ { N } \delta \left( \tau - t _ { i } \right) \left( \varepsilon _ { i } ^ { \mu } \frac \partial { \partial t _ { i } } + i \; k _ { i } ^ { \mu } \right) | J^ \mu ( \tau ) = \sum _i= 1 ^N \delta \left ( \tau -t_i \right ) \left ( \varepsilon _i^ \mu \frac {\partial} {\partial t_i} +i \; k_i^ \mu \right )
L ^ { \xi } = p \cdot \hat { H } + X ^ { \xi } , \quad X ^ { \xi } = i \sum _ { \rho \in \Delta _ { + } } g _ { | \rho | } ( \rho \cdot \hat { H } ) \left( x ( \rho \cdot q ) - x ( \rho ^ { \vee } \! \cdot \hat { H } \xi ) \right) \hat { s } _ { \rho } , | L^ \xi =p \cdot \hat { H+X^ } \xi , \quad X^ \xi =i \sum _ \rho \in \Delta _+g_| \rho |( \rho \cdot \hat { H) } \left ( x( \rho \cdot q)-x( \rho ^ \vee \! \cdot \hat { H } \xi ) \right ) \hat { s_ } \rho ,
\Gamma _ { 0 } = \frac { \alpha \, M _ { Z } } { 3 } \qquad \mathrm { a n d } \qquad \mu _ { f } = \frac { m _ { f } ^ { 2 } } { M _ { Z } ^ { 2 } } \; . | \Gamma _ 0 = \frac {\alpha \,M_Z} {3} \qquad \mathrm { a } n d \qquad \mu _f= \frac {m_f^2} {M_Z^2} \; .
\begin{align*}S = - \; \frac{1}{4}\; \int d^{4}x (G_{\mu\nu}(A), G^{\mu\nu}(A))\end{align*} | \begin {aligned} S=- \; \frac {1} {4} \; \int d^ 4 x(G_ \mu \nu ( A),G^ \mu \nu ( A)) \end {aligned}
\begin{align*}J_2(N; \theta) := \int_0^{\infty} e^{-\theta t} \left(1 - e^{-(1 - \theta) t / N} \right)^N dt.\end{align*} | \begin {aligned} J_ {2} ( N; \theta ) := \int _ {0} ^ \infty e^- \theta t \left ( 1 -e^-( 1 - \theta ) t/N \right ) ^ {N} d t. \end {aligned}
L _ { \mathrm { ~ v ~ a ~ r ~ i ~ a ~ t ~ i ~ o ~ n ~ } } | L_ \mathrm { ~ } v ~ a ~ r ~ i ~ a ~ t ~ i ~ o ~ n ~
a _ { i } \to a _ { i } + \Delta \qquad \forall _ { j \in \partial j } \: \: \omega _ { i j } \to \omega _ { i j } - \frac \Delta 2 . | a_i \to a_i+ \Delta \qquad \forall _j \in \partial j \: \: \omega _i j \to \omega _i j- \frac {\Delta} {2} .
\begin{align*}\nabla u(x)=\sum_{i=1}^{n+1} \alpha_i(x)\xi_i(x).\end{align*} | \begin {aligned} \nabla u(x)= \sum _i= 1 ^n+ 1 \alpha _ {i} ( x) \xi _ {i} ( x). \end {aligned}
\begin{align*} \theta=-\frac{1}{n}\delta\omega\circ J;\end{align*} | \begin {aligned} \theta =- \frac {1} {n} \delta \omega \circ J; \end {aligned}
\mathrm { ~ P ~ } ( X _ { 0 : T } , f \mid \mathcal { O } _ { 1 : K } ) | \mathrm { ~ } P ~(X_ 0 :T,f \mid \mathcal { O_ } 1 :K)
\begin{array} { r l } { \frac { 1 } { 2 q ( 2 q - 1 ) } \dot { V } _ { u } ( y ) } & { \leq - c \int _ { 0 } ^ { L } \Big ( y _ { z } ( z ) \Big ) ^ { 2 q - 2 } \Big ( y _ { z z } ( z ) \Big ) ^ { 2 } d z - \int _ { 0 } ^ { L } \eta \left( \left( y _ { z } ( z ) \right) ^ { 2 q } \right) d z } \\ & { \quad - \int _ { 0 } ^ { L } \Big ( y _ { z } ( z ) \Big ) ^ { 2 q - 2 } y _ { z z } ( z ) v ( z ) d z . } \end{array} | \begin {array} {r l} \frac {1} {2 q(2 q-1)} \dot { V_u(y) } & \leq -c \int _ 0 ^L \Big ( y_z(z) \Big ) ^ 2 q- 2 \Big ( y_z z(z) \Big ) ^ 2 d z- \int _ 0 ^L \eta \left ( \left ( y_z(z) \right ) ^ 2 q \right ) d z \\ & \quad - \int _ 0 ^L \Big ( y_z(z) \Big ) ^ 2 q- 2 y_z z(z) v(z) d z. \end {array}
\beta _ { H D } = \frac { \alpha ^ { 2 } } { \pi } + O ( \alpha ^ { 4 } ) . | \beta _H D= \frac {\alpha^2} {\pi} +O( \alpha ^ 4 ) .
y = 0 | y= 0
\begin{align*}\Lambda_{r,n}^{d}=\binom{n}{r}\frac{(2d+1)!!}{2^{d}(d+1)!}\sum_{j=0}^{d}(-1)^{d-j}\binom{d+\frac{1}{2}}{j}\binom{d+\frac{1}{2}}{d-j}\frac{\Gamma(r+j+\frac{3}{2})\Gamma(n+d-r-j+\frac{3}{2})}{\Gamma(n+d+3)},\end{align*} | \begin {aligned} \Lambda _r,n^d= \binom {n} {r} \frac {(2 d+1) ! !} {2^d(d+1) !} \sum _j= 0 ^d(- 1 ) ^d-j \binom {d+\frac{1}{2}} {j} \binom {d+\frac{1}{2}} {d-j} \frac {\Gamma(r+j+\frac{3}{2}) \Gamma(n+d-r-j+\frac{3}{2})} {\Gamma(n+d+3)} , \end {aligned}
\pi | \pi
\boldsymbol { U } _ { \u { \tau } } : \mathbb { E } _ { \mathrm { R } } \rightarrow \mathbb { E } _ { \mathrm { R } } | \boldsymbol { U_ } \u \tau : \mathbb { E_ } \mathrm { R } \rightarrow \mathbb { E_ } \mathrm { R }
e ^ { + p } = \sqrt { \rho ^ { + + } } G ^ { p q } d { \tilde { \eta } } ^ { q } | e^+p= \sqrt { \rho } ^++G^p q d \tilde { \eta } ^q
\begin{align*} \sum_{i=1}^n\gamma_im_iv_i=0,\end{align*} | \begin {aligned} \sum _i= 1 ^ {n} \gamma _ {i} m_ {i} v_ {i} = 0 , \end {aligned}
\boxminus | \boxminus
G = ( E _ { i } - H + i \epsilon ) ^ { - 1 } | G=(E_i-H+i \epsilon ) ^- 1
D _ { n } | D_n
\begin{array} { r l r } { \mathrm { F o r ~ \varsigma > 0 ~ } : \quad \tilde { I } ( \tau , \varsigma ) } & { = } & { + \varsigma \int _ { 0 } ^ { \tau } d \tau ^ { \prime } e ^ { - \varsigma ( \tau - \tau ^ { \prime } ) } \tilde { B } ( \tau ^ { \prime } ) + e ^ { - \varsigma \tau } \tilde { I } ( 0 , \varsigma ) } \\ { \mathrm { F o r ~ \varsigma < 0 ~ } : \quad \tilde { I } ( \tau , \varsigma ) } & { = } & { - \varsigma \int _ { \tau } ^ { \tau _ { \infty } } d \tau ^ { \prime } e ^ { - \varsigma ( \tau - \tau ^ { \prime } ) } \tilde { B } ( \tau ^ { \prime } ) } \end{array} | \begin {array} {r l r} \mathrm { F } o r ~ \  v a r s i g m a> 0 ~: \quad \tilde { I( } \tau , \varsigma ) &=&+ \varsigma \int _ 0 ^ \tau d \tau ^ \prime e^- \varsigma ( \tau - \tau ^ \prime ) \tilde { B( } \tau ^ \prime ) +e^- \varsigma \tau \tilde { I( } 0 , \varsigma ) \\ \mathrm { F } o r ~ \  v a r s i g m a< 0 ~: \quad \tilde { I( } \tau , \varsigma ) &=&- \varsigma \int _ \tau ^ \tau _ \infty d \tau ^ \prime e^- \varsigma ( \tau - \tau ^ \prime ) \tilde { B( } \tau ^ \prime ) \end {array}
\begin{align*}\mathfrak{F}(t)=\mathfrak{F}(u,w):=\int_\Omega u \log u+\frac{1}{2}\int_\Omega u w\textrm{on}(0,T_{max}),\end{align*} | \begin {aligned} \mathfrak { F(t)= } \mathfrak { F(u,w):= } \int _ {\Omega} u \mathrm{log} u+ \frac {1} {2} \int _ {\Omega} u w \textrm { o } n( 0 ,T_m a x), \end {aligned}
\beta ^ { z } ( \gamma ( z ) ) = \frac { d \gamma } { d z } \beta ^ { z } ( z ) = ( c z + d ) ^ { - 2 } \beta ^ { z } ( z ) | \beta ^z( \gamma ( z))= \frac {d \gamma} {d z} \beta ^z(z)=(c z+d)^- 2 \beta ^z(z)
\begin{align*}\frac{\Phi_{prim}^{odd}(Q)}{\Phi(Q)} = 1 +O(\frac 1q)\;,\end{align*} | \begin {aligned} \frac {\Phi_p r i m^o d d(Q)} {\Phi(Q)} = 1 +O( \frac {1} {q} ) \; , \end {aligned}
\begin{align*}\Pi (V)=\{\lambda \in {\cal H}^*|V_\lambda\neq 0\}\,,~~~~D(\lambda)=\{\mu\in{\cal H}^*|\mu\leq \lambda\,,~\lambda\in{\cal H}^*\}\end{align*} | \begin {aligned} \Pi ( V)= \{ \lambda \in \cal H^ {*} | V_ {\lambda} \neq 0 \} \, ,~ ~ ~ ~ D( \lambda ) = \{ \mu \in \cal H^ {*} | \mu \leq \lambda \, ,~ \lambda \in \cal H^ {*} \} \end {aligned}
\begin{align*}J_{S}(f^n) = J_{S}(f^{nq}) = J_{S}(f^{q}).\end{align*} | \begin {aligned} J_S(f^ {n} ) =J_S(f^n q)=J_S(f^q). \end {aligned}
\begin{align*} \mu=\frac{1}{\epsilon}(\chi^3-\chi)-\frac{\epsilon}{\rho}\chi_{xx}.\end{align*} | \begin {aligned} \mu = \frac {1} {\epsilon} ( \chi ^ {3} - \chi ) - \frac {\epsilon} {\rho} \chi _x x. \end {aligned}
\begin{align*}{\cal E}_l = -\eta_{11}\left(a_4-a_l\right){\rm E}_l\end{align*} | \begin {aligned} \cal E_ {l} =- \eta _ 1 1 \left ( a_ {4} -a_ {l} \right ) \mathrm { E_ } {l} \end {aligned}
\begin{align*} y^2z+ a_1 xy z + a_3 yz^2 -(x^3+ a_2 x^2 z + a_4 x z^2 + a_6 z^3)=0,\end{align*} | \begin {aligned} y^ {2} z+a_ {1} x y z+a_ {3} y z^ {2} -(x^ {3} +a_ {2} x^ {2} z+a_ {4} x z^ {2} +a_ {6} z^ {3} ) = 0 , \end {aligned}
\Delta | \Delta
\begin{gather*}\Phi=\Delta_Y^{-1}\circ(1\otimes\rho)\circ\Delta_X\end{gather*} | \begin {gather*} \Phi = \Delta _Y^ {-1} \circ ( 1 \otimes \rho ) \circ \Delta _X \end {gather*}
\delta _ { a b } \delta _ { c d } + \delta _ { a c } \delta _ { b d } + \delta _ { a d } \delta _ { b c } \: . | \delta _a b \delta _c d+ \delta _a c \delta _b d+ \delta _a d \delta _b c \: .
0 . 0 1 9 | 0 . 0 1 9
\begin{align*} J^E(x,t,k)=\left\{ \begin{aligned} & \Delta_\eta J^{(3)}(x,t,k){\Delta_\eta}^{-1}, & k\in\Gamma^{(3)}\backslash \left(U_{\delta}(\eta)\cup U_{\delta}(-\eta)\right),\\ & m_{\pm\eta}(x,t,k)\Delta_\eta^{-1}, & k\in\partial U_{\delta}(\pm\eta), \end{aligned} \right. \end{align*} | \begin {align*} J^E(x,t,k)= \left \{ \begin {aligned} & \Delta _ \eta J^ {(3)} ( x,t,k) {\Delta_\eta} ^ {-1} ,& k \in \Gamma ^ {(3)} \backslash \left ( U_ {\delta} ( \eta ) \cup U_ {\delta} ( - \eta ) \right ) , \\ & m_ {\pm\eta} ( x,t,k) \Delta _ \eta ^ {-1} ,& k \in \partial U_ {\delta} ( \pm \eta ) , \end {aligned} \right . \end {align*}
\begin{array} { r l } { \mathbf { L } ^ { \prime } } & { { } = \gamma ( \mathbf { v } ) ( \mathbf { L } + v \mathbf { n } \times \mathbf { N } ) - ( \gamma ( \mathbf { v } ) - 1 ) ( \mathbf { L } \cdot \mathbf { n } ) \mathbf { n } } \\ { \mathbf { N } ^ { \prime } } & { { } = \gamma ( \mathbf { v } ) \left( \mathbf { N } - { \frac { v } { c ^ { 2 } } } \mathbf { n } \times \mathbf { L } \right) - ( \gamma ( \mathbf { v } ) - 1 ) ( \mathbf { N } \cdot \mathbf { n } ) \mathbf { n } } \end{array} | \begin {array} {r l} \mathbf { L^ } \prime &= \gamma ( \mathbf { v)( } \mathbf { L+v } \mathbf { n } \times \mathbf { N)-( } \gamma ( \mathbf { v)- } 1 ) ( \mathbf { L } \cdot \mathbf { n) } \mathbf { n } \\ \mathbf { N^ } \prime &= \gamma ( \mathbf { v) } \left ( \mathbf { N- } \frac {v} {c^2} \mathbf { n } \times \mathbf { L } \right ) -( \gamma ( \mathbf { v)- } 1 ) ( \mathbf { N } \cdot \mathbf { n) } \mathbf { n } \end {array}
F _ { k l } ~ = ~ { \textstyle { \frac { 1 } { 2 } } } \, \epsilon _ { k l m } e _ { m } ~ ~ , ~ ~ F _ { k l } ^ { \prime } ~ = ~ - \, { \textstyle { \frac { 1 } { 2 } } } \, \epsilon _ { k l m } e _ { m } \qquad ( 1 \leq k < l \leq 3 ) ~ . | F_k l ~=~ \textstyle \frac {1} {2} \, \epsilon _k l m e_m ~ ~,~ ~ F_k l^ \prime ~=~- \, \textstyle \frac {1} {2} \, \epsilon _k l m e_m \qquad ( 1 \leq k<l \leq 3 ) ~.
\biggl ( L \biggl ( x - 1 , { \frac { \partial } { \partial x } } \biggr ) - z ^ { p } \biggr ) \Psi ( x , 0 , z ) = 0 \, \ \mathrm { a n d } \ \, \biggl ( L \biggl ( z , { \frac { \partial } { \partial z } } - 1 \biggr ) - ( x - 1 ) ^ { p } \biggr ) \Psi ( x , 0 , z ) = 0 \, , | \biggl ( L \biggl ( x- 1 , \frac {\partial} {\partial x} \biggr ) -z^p \biggr ) \Psi ( x, 0 ,z)= 0 \, \ \ mathrm a n d \ \ , \biggl ( L \biggl ( z, \frac {\partial} {\partial z} - 1 \biggr ) -(x- 1 ) ^p \biggr ) \Psi ( x, 0 ,z)= 0 \, ,
\frac { M ( r ) } { r } = 2 \varepsilon _ { A } \left( f _ { A } ( r ) + \dot { r } ^ { 2 } \right) ^ { \frac { 1 } { 2 } } , | \frac {M(r)} {r} = 2 \varepsilon _A \left ( f_A(r)+ \dot { r^ } 2 \right ) ^ \frac {1} {2} ,
\begin{align*}g_1'(0)=\frac{L}{\pi}\frac{\sqrt{\pi}}{3}\Gamma(-1/2)\left(\zeta(-4,3/2)-\frac{1}{4}\zeta(-2,3/2)\right)=0,\end{align*} | \begin {aligned} g_ {1} ^ \prime ( 0 ) = \frac {L} {\pi} \frac {\sqrt \pi} {3} \Gamma ( - 1 / 2 ) \left ( \zeta ( - 4 , 3 / 2 ) - \frac {1} {4} \zeta ( - 2 , 3 / 2 ) \right ) = 0 , \end {aligned}
\Big ( - \triangle + m ^ { 2 } \Big ) C _ { m } ( x - y ) = \delta ( x - y ) . | \Big ( - \triangle +m^ 2 \Big ) C_m(x-y)= \delta ( x-y).
\xi _ { f } | \xi _f
\begin{align*}g'(\delta) = 2Q-L_0T\sinh \delta/2, g''(\delta) = -L_0T\cosh \delta /2 < 0,\end{align*} | \begin {aligned} g^ \prime ( \delta ) = 2 Q-L_ {0} T \mathrm{sinh} \delta / 2 ,g^ \prime \prime ( \delta ) =-L_ {0} T \mathrm{cosh} \delta / 2 < 0 , \end {aligned}
\begin{array} { r l } { \textsc { I S } [ P _ { \mathrm { L } } ] } & { { } = \frac { 1 3 } { 1 2 } \left( u _ { i - 2 } - 2 u _ { i - 1 } + u _ { i } \right) ^ { 2 } + \frac { 1 } { 4 } \left( u _ { i - 2 } - 4 u _ { i - 1 } + 3 u _ { i } \right) ^ { 2 } , } \\ { \textsc { I S } [ P _ { \mathrm { C } } ] } & { { } = \frac { 1 3 } { 1 2 } \left( u _ { i - 1 } - 2 u _ { i } + u _ { i + 1 } \right) ^ { 2 } + \frac { 1 } { 4 } \left( u _ { i + 1 } - u _ { i - 1 } \right) ^ { 2 } , } \\ { \textsc { I S } [ P _ { \mathrm { R } } ] } & { { } = \frac { 1 3 } { 1 2 } \left( u _ { i } - 2 u _ { i + 1 } + u _ { i + 2 } \right) ^ { 2 } + \frac { 1 } { 4 } \left( 3 u _ { i } - 4 u _ { i + 1 } + u _ { i + 2 } \right) ^ { 2 } , } \end{array} | \begin {array} {r l} \textsc { I } S [P_\mathrm L] &= \frac {1 3} {1 2} \left ( u_i- 2 - 2 u_i- 1 +u_i \right ) ^ 2 + \frac {1} {4} \left ( u_i- 2 - 4 u_i- 1 + 3 u_i \right ) ^ 2 , \\ \textsc { I } S [P_\mathrm C] &= \frac {1 3} {1 2} \left ( u_i- 1 - 2 u_i+u_i+ 1 \right ) ^ 2 + \frac {1} {4} \left ( u_i+ 1 -u_i- 1 \right ) ^ 2 , \\ \textsc { I } S [P_\mathrm R] &= \frac {1 3} {1 2} \left ( u_i- 2 u_i+ 1 +u_i+ 2 \right ) ^ 2 + \frac {1} {4} \left ( 3 u_i- 4 u_i+ 1 +u_i+ 2 \right ) ^ 2 , \end {array}
\begin{align*}\|\sum_{k > 0} u_{<k} v_k\|_{L^2(Q)} \lesssim & \ \|\sum_{k > 0} \chi_{Q} u_{<k} v_k\|_{L^2(Q)}\lesssim \|u\|_{L^\infty} ( \sum_k \| \chi_Q v_k\|^2_{L^2(Q)})^\frac12\\ \lesssim & \ \| u\|_{L^\infty} \| \chi_Q S_{>0}(v)\|_{L^2(Q)}.\lesssim \|u\|_{L^\infty} \|v\|_{BMO}.\end{align*} | \begin {aligned} \| \sum _k> 0 u_<k v_ {k} \| _L^ {2} ( Q) \lesssim & \ \ | \sum _k> 0 \chi _Q u_<k v_ {k} \| _L^ {2} ( Q) \lesssim \| u \| _L^ {\infty} ( \sum _ {k} \| \chi _ {Q} v_ {k} \| _L^ {2} ( Q)^ {2} ) ^ {\frac{1}{2}} \\ \lesssim & \ \ | u \| _L^ {\infty} \| \chi _ {Q} S_> 0 ( v) \| _L^ {2} ( Q). \lesssim \| u \| _L^ {\infty} \| v \| _B M O. \end {aligned}
V _ { a b c _ { 2 } d _ { 2 } } ^ { ( 3 ) } = \frac { 1 } { 2 } \rho ^ { 3 } v _ { a b c _ { 2 } d _ { 2 } } ^ { ( 3 ) } ( \frac { 1 } { 4 } ( ( \Sigma _ { r e n . } ^ { 1 } ) ^ { 2 } + 2 \Sigma _ { r e n . } ^ { 1 } D _ { 1 } + D _ { 1 } ^ { 2 } ) - \frac { 1 } { 2 } D _ { 1 } \Sigma _ { r e n . } ^ { 1 } - \frac { 1 } { 2 } D _ { 1 } ^ { 2 } ) . | V_a b c_ 2 d_ 2 ^( 3 ) = \frac {1} {2} \rho ^ 3 v_a b c_ 2 d_ 2 ^( 3 ) ( \frac {1} {4} ( ( \Sigma _r e n.^ 1 ) ^ 2 + 2 \Sigma _r e n.^ 1 D_ 1 +D_ 1 ^ 2 ) - \frac {1} {2} D_ 1 \Sigma _r e n.^ 1 - \frac {1} {2} D_ 1 ^ 2 ) .
\arg \left( \left. \frac { d w } { d z } \right| _ { \zeta = \overline { { \zeta _ { i } } } } \right) = - \frac { \pi } { 2 } , \qquad i = 1 , 2 , | \mathrm{arg} \left ( \left . \frac {d w} {d z} \right |_ \zeta = \overline { {\zeta_i} } \right ) =- \frac {\pi} {2} , \qquad i= 1 , 2 ,
{ \left( \begin{array} { l } { \rho _ { \mathrm { e } } } \\ { \rho _ { \mathrm { m } } } \end{array} \right) } = { \left( \begin{array} { l l } { \cos \xi } & { - \sin \xi } \\ { \sin \xi } & { \cos \xi } \end{array} \right) } { \left( \begin{array} { l } { \rho _ { \mathrm { e } } ^ { \prime } } \\ { \rho _ { \mathrm { m } } ^ { \prime } } \end{array} \right) } | \left ( \begin {array} {l} \rho _ \mathrm { e } \\ \rho _ \mathrm { m } \end {array} \right ) = \left ( \begin {array} {l l} \mathrm{cos} \xi &- \mathrm{sin} \xi \\ \mathrm{sin} \xi & \mathrm{cos} \xi \end {array} \right ) \left ( \begin {array} {l} \rho _ \mathrm { e^ } \prime \\ \rho _ \mathrm { m^ } \prime \end {array} \right )
\begin{align*}\int_{\mathcal{M}_m} \Phi(y)dy= \int_{G_m}S_{L,v}(diag(g,I_{m+1}))|g|^{s_0}dg.\end{align*} | \begin {aligned} \int _ \mathcal { M_ } {m} \Phi ( y) d y= \int _G_ {m} S_L,v(d i a g(g,I_m+ 1 ) ) | g |^s_ {0} d g. \end {aligned}
\xi ( x ) \propto 1 / \sqrt { \Gamma _ { \mathrm { i m p } } ( x ) } \propto 1 / \sqrt { F ( x ) } | \xi ( x) \propto 1 / \sqrt { \Gamma } _ \mathrm { i } m p(x) \propto 1 / \sqrt { F(x) }
\gamma ^ { k } = \sum _ { q = 1 } ^ { M } \left( A _ { q k } ^ { * } c _ { q } + B _ { q k } ^ { * } c _ { q } ^ { \dag } \right) , | \gamma ^k= \sum _q= 1 ^M \left ( A_q k^*c_q+B_q k^*c_q^ \dag \right ) ,
O ( M ( n ) n ^ { 1 / 2 } ) | O(M(n) n^ 1 / 2 )
\begin{align*} 2 \binom{3m+1}{3} - 4 \binom{m+2}{3} = 3 \sum_{i=1}^{m} x_{i}^{2} + 2(m+1)h^{2}.\end{align*} | \begin {aligned} 2 \binom {3 m+1} {3} - 4 \binom {m+2} {3} = 3 \sum _i= 1 ^m x_i^ 2 + 2 ( m+ 1 ) h^ 2 . \end {aligned}
\begin{align*} \mathcal{R}_0 & = \mathcal{R} \setminus ( \mathcal{R}_i \sqcup \mathcal{R}_j ) \\ \mathcal{R}_1 & = \mathcal{R} \cap \mathcal{G}_{l_1} \\ \mathcal{R}_2 & = \mathcal{R} \cap \mathcal{G}_{l_2} \end{align*} | \begin {aligned} \mathcal { R_ } {0} &= \mathcal { R } \setminus ( \mathcal { R_ } {i} \sqcup \mathcal { R_ } {j} ) \\ \mathcal { R_ } {1} &= \mathcal { R } \cap \mathcal { G_l_ } {1} \\ \mathcal { R_ } {2} &= \mathcal { R } \cap \mathcal { G_l_ } {2} \end {aligned}
S = \sigma \int d ^ { 2 } \xi \partial _ { a } x ^ { 0 } \partial _ { a } x ^ { 0 } + \sigma \int d ^ { 2 } \xi \partial _ { a } x ^ { i } \partial _ { a } x ^ { i } | S= \sigma \int d^ 2 \xi \partial _a x^ 0 \partial _a x^ 0 + \sigma \int d^ 2 \xi \partial _a x^i \partial _a x^i
\varphi ( \xi ) = \arg \left( { \hat { f } } ( \xi ) \right) , | \varphi ( \xi ) = \mathrm{arg} \left ( \hat { f( } \xi ) \right ) ,
\tau = T _ { i } - t | \tau =T_i-t
\begin{align*}&v_i^n(\rho) \doteq \bar{w}_i^n - p(\rho),& i=0,\ldots,N_n-1.\end{align*} | \begin {aligned} & v_ {i} ^ {n} ( \rho ) \doteq \bar { w_ } {i} ^ {n} -p( \rho ) ,& i= 0 , . . . ,N_ {n} - 1 . \end {aligned}
