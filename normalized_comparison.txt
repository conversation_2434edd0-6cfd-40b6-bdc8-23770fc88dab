d s ^ { 2 } = e ^ { f ( z ) } \left( \eta _ { \mu \nu } d x ^ { \mu } d x ^ { \nu } \right) + d z ^ { 2 } , | d s^ {2} =e^ {f(z)} \left ( \eta _ {\mu \nu} d x^ {\mu} d x^ {\nu} \right ) +d z^ {2} ,
a _ { 1 } | a_ {1}
\begin{array} { r l } & { \frac { 1 } { 2 } \frac { \textup { d } } { \, \textup { d } t } \sum _ { i = + , - } \int _ { 0 } ^ { 1 } | f _ { i } | ^ { 2 } \, \textup { d } y + \frac { D _ { T } } { L ^ { 2 } ( t ) } \sum _ { i = + , - } \int _ { 0 } ^ { 1 } | \partial _ { y } f _ { i } | ^ { 2 } \, \textup { d } y } \\ & { \ge \frac { 1 } { 2 } \frac { \textup { d } } { \, \textup { d } t } \sum _ { i = + , - } \int _ { 0 } ^ { 1 } | f _ { i } | ^ { 2 } \, \textup { d } y + \frac { D _ { T } } { ( L ^ { ( 0 ) } + T \| h \| _ { L ^ { \infty } ( \mathbb { R } ) } ) ^ { 2 } } \sum _ { i = + , - } \int _ { 0 } ^ { 1 } | \partial _ { y } f _ { i } | ^ { 2 } \, \textup { d } y . } \end{array} | \begin {array} {r l} & {{\frac{1}{2} \frac{\textup{d}}{\,\textup{d} t} \sum_{i=+,-} \int_{0}^{1} | f_{i} |^{2}\,\textup{d} y+\frac{D_{T}}{L^{2}(t)} \sum_{i=+,-} \int_{0}^{1} | \partial_{y} f_{i} |^{2}\,\textup{d} y}} \\ & {{\ge \frac{1}{2} \frac{\textup{d}}{\,\textup{d} t} \sum_{i=+,-} \int_{0}^{1} | f_{i} |^{2}\,\textup{d} y+\frac{D_{T}}{(L^{(0)}+T \| h \|_{L^{\infty}(\mathbb{R})})^{2}} \sum_{i=+,-} \int_{0}^{1} | \partial_{y} f_{i} |^{2}\,\textup{d} y.}} \end {array}
z | z
\sigma | \sigma
\alpha _ { v } = \frac { 1 } { s _ { v } } \, \iota _ { v } \, \omega _ { \xi } \ \in \ \mathsf { \Gamma } ( { \mathrm { A n n } } ( v ) ) \ . | \alpha _ {v} = \frac {1} {s_{v}} \, \iota _ {v} \, \omega _ {\xi} \ \ in \ \ mathsf {\Gamma} ( {\mathrm{Ann}} ( v)).
{ \cal L } _ { e f f } = - \frac { 1 } { 4 } G _ { \mu \nu } ^ { a } G _ { \mu \nu } ^ { a } + \frac { m } { 4 } \varepsilon ^ { \alpha \beta \gamma } \left( G _ { \alpha \beta } ^ { a } A _ { \gamma } ^ { a } - \frac { g } { 3 } \varepsilon _ { a b c } A _ { \alpha } ^ { a } A _ { \beta } ^ { b } A _ { \gamma } ^ { c } \right) + \frac { 1 } { 2 } A _ { 0 } ^ { a } \Pi _ { 0 0 } ( T ) \delta ^ { a b } A _ { 0 } ^ { b } , | {\cal L} _ {e f f} =- \frac {1} {4} G_ {\mu \nu} ^ {a} G_ {\mu \nu} ^ {a} + \frac {m} {4} \varepsilon ^ {\alpha \beta \gamma} \left ( G_ {\alpha \beta} ^ {a} A_ {\gamma} ^ {a} - \frac {g} {3} \varepsilon _ {a b c} A_ {\alpha} ^ {a} A_ {\beta} ^ {b} A_ {\gamma} ^ {c} \right ) + \frac {1} {2} A_ {0} ^ {a} \Pi _ {0 0} ( T) \delta ^ {a b} A_ {0} ^ {b} ,
\begin{align*}\mathcal{H} =\mathrm{Aut} \, \bigotimes \, \mathcal{H}^\prime\end{align*} | \begin {aligned} {\mathcal{H}=\mathrm{Aut}\,\bigotimes\,\mathcal{H}^{\prime}} \end {aligned}
\begin{align*}\mathrm{tr}\left(G_0FG_0F\right)=4\mathrm{tr}\left(\left(\Delta\delta_{\mu\nu}+\Delta\partial_\mu\partial_\nu\Delta\right)\bar{F}_{\nu\sigma}\left(\Delta\delta_{\sigma\tau}+\Delta\partial_\sigma\partial_\tau\Delta\right)\bar{F}_{\tau\mu}\right)+\textrm{ cubic}\end{align*} | \begin {aligned} {\mathrm{tr} \left(G_{0} F G_{0} F \right)=4 \mathrm{tr} \left(\left(\Delta \delta_{\mu \nu}+\Delta \partial_{\mu} \partial_{\nu} \Delta \right) \bar{F}_{\nu \sigma} \left(\Delta \delta_{\sigma \tau}+\Delta \partial_{\sigma} \partial_{\tau} \Delta \right) \bar{F}_{\tau \mu} \right)+\textrm{c u b i c}} \end {aligned}
\begin{align*} {\varepsilon}=F(x^0,x^1,Y)\,\frac{\Delta\sqrt{-\det g^{ij}}}{{L}_2{L}_3},\end{align*} | \begin {aligned} {{\varepsilon}=F(x^{0},x^{1},Y)\,\frac{\Delta \sqrt{-\det g^{i j}}}{{L}_{2}{L}_{3}},} \end {aligned}
x = \frac { 2 } { \tan \theta } \xi \eta + \xi - \frac { 1 } { \tan \theta } \eta + \frac { 1 } { \tan \theta } , \quad y = \eta , | x= \frac {2} {\tan \theta} \xi \eta + \xi - \frac {1} {\tan \theta} \eta + \frac {1} {\tan \theta} , \quady = \eta ,
\begin{align*}\sum_{K\in\mathcal{T}_h}\sum_{i=1}^3(L_3+L_6)n_i\leq Ch^2|u|_{H^4(\Omega)}|w_h|_h. \end{align*} | \begin {aligned} {\sum_{K \in \mathcal{T}_{h}} \sum_{i=1}^{3}(L_{3}+L_{6}) n_{i} \leq C h^{2} | u |_{H^{4}(\Omega)} | w_{h} |_{h}.} \end {aligned}
\begin{array} { r } { Q _ { \mathrm { W } + j } - \hat { Q } _ { \mathrm { W } + j } = 0 \quad ( j = 1 , \cdots , j _ { \operatorname* { m a x } } ) . } \end{array} | \begin {array} {r} {{Q_{\mathrm{W}+j}-\hat{Q}_{\mathrm{W}+j}=0\quad(j=1,. . .,j_{\operatorname*{m a x}}).}} \end {array}
\begin{align*}\max(|R_{31}|,|R_{32}|) = O \biggl( \max \biggl\{ \frac{k^{\frac{\alpha}{\alpha+d} - \epsilon}}{n^{\frac{\alpha}{\alpha+d} - \epsilon}}\, , \, \frac{k^{\frac{\beta}{d}}}{n^{\frac{\beta}{d}}} \biggr\} \biggr),\end{align*} | \begin {aligned} {\max(| R_{3 1} |,| R_{3 2} |)=O \biggl(\max \biggl \{\frac{k^{\frac{\alpha}{\alpha+d}-\epsilon}}{n^{\frac{\alpha}{\alpha+d}-\epsilon}}\,,\,\frac{k^{\frac{\beta}{d}}}{n^{\frac{\beta}{d}}} \biggr \} \biggr),} \end {aligned}
\begin{align*}1-\lambda+\alpha\sqrt{ab}-\beta\frac{a+b}{2ab}=0.\end{align*} | \begin {aligned} {1-\lambda+\alpha \sqrt{a b}-\beta \frac{a+b}{2 a b}=0.} \end {aligned}
{ \frac { d f ^ { \mu \nu } } { d t } } = f ^ { \mu \lambda } U _ { \lambda } ^ { \nu } - f ^ { \nu \lambda } U _ { \lambda } ^ { \mu } | {\frac{d f^{\mu \nu}}{d t}} =f^ {\mu \lambda} U_ {\lambda} ^ {\nu} -f^ {\nu \lambda} U_ {\lambda} ^ {\mu}
\} | \}
\begin{array} { r l } { M ^ { ( 0 ) } } & { { } = \sum _ { i = 0 } ^ { Q - 1 } f _ { i } ^ { \lambda } , } \\ { M _ { \alpha } ^ { ( 1 ) } } & { { } = \sum _ { i = 0 } ^ { Q - 1 } f _ { i } ^ { \lambda } v _ { i \alpha } ^ { \lambda } , } \\ { M _ { \alpha \beta } ^ { ( 2 ) } } & { { } = \sum _ { i = 0 } ^ { Q - 1 } f _ { i } ^ { \lambda } v _ { i \alpha } ^ { \lambda } v _ { i \beta } ^ { \lambda } , } \\ { M _ { \alpha \beta \gamma } ^ { ( 3 ) } } & { { } = \sum _ { i = 0 } ^ { Q - 1 } f _ { i } ^ { \lambda } v _ { i \alpha } ^ { \lambda } v _ { i \beta } ^ { \lambda } v _ { i \gamma } ^ { \lambda } , } \\ { M _ { \alpha \beta } ^ { ( 4 ) } } & { { } = \sum _ { i = 0 } ^ { Q - 1 } f _ { i } ^ { \lambda } v _ { i \alpha } ^ { \lambda } v _ { i \beta } ^ { \lambda } v _ { i \gamma } ^ { \lambda } v _ { i \gamma } ^ { \lambda } , } \end{array} | \begin {array} {r l} {M^{(0)}} & {=\sum_{i=0}^{Q-1} f_{i}^{\lambda},} \\ {M_{\alpha}^{(1)}} & {=\sum_{i=0}^{Q-1} f_{i}^{\lambda} v_{i \alpha}^{\lambda},} \\ {M_{\alpha \beta}^{(2)}} & {=\sum_{i=0}^{Q-1} f_{i}^{\lambda} v_{i \alpha}^{\lambda} v_{i \beta}^{\lambda},} \\ {M_{\alpha \beta \gamma}^{(3)}} & {=\sum_{i=0}^{Q-1} f_{i}^{\lambda} v_{i \alpha}^{\lambda} v_{i \beta}^{\lambda} v_{i \gamma}^{\lambda},} \\ {M_{\alpha \beta}^{(4)}} & {=\sum_{i=0}^{Q-1} f_{i}^{\lambda} v_{i \alpha}^{\lambda} v_{i \beta}^{\lambda} v_{i \gamma}^{\lambda} v_{i \gamma}^{\lambda},} \end {array}
\delta Z ^ { M } = \eta ^ { i } ( \xi ) \partial _ { i } Z ^ { M } , \quad \delta h _ { i j } = \eta ^ { k } \partial _ { k } h _ { i j } + 2 \partial _ { ( i } \eta ^ { k } h _ { j ) k } \; . | \delta Z^ {M} = \eta ^ {i} ( \xi ) \partial _ {i} Z^ {M} , \quad \delta h_ {i j} = \eta ^ {k} \partial _ {k} h_ {i j} + 2 \partial _ {(i} \eta ^ {k} h_ {j) k} \; .
\beta F = - 2 e ^ { - \beta \sqrt { 3 / 2 } } \left( 1 + \beta \sqrt { 2 / 3 } + \cdots \right) + { \cal O } \left( e ^ { - \beta \sqrt { 6 } } \right) . | \beta F=- 2 e^ {-\beta \sqrt{3/2}} \left ( 1 + \beta \sqrt { {2/3} } + . . . \right ) + {\cal O} \left ( e^ {-\beta \sqrt{6}} \right ) .
\bar { F } | \bar { {F} }
\lambda \ll 1 | \lambda \ll 1
\begin{align*}S^i(n) = 2 \cdot 3^i 4^{ v_2-i} q_2 + 1 \equiv 1 \pmod{4} \end{align*} | \begin {aligned} {S^{i}(n)=2 \cdot 3^{i} 4^{v_{2}-i} q_{2}+1 \equiv 1 \pmod{4}} \end {aligned}
\begin{align*} (id-\Phi_{T_1})\circ \cdots \circ (id-\Phi_{T_k})({\bf K_{T}^*}{\bf K_{T}})={\bf \Delta_{T}}(I).\end{align*} | \begin {aligned} {(i d-\Phi_{T_{1}}) \circ . . . \circ(i d-\Phi_{T_{k}})({\bf K_{T}^{*}}{\bf K_{T}})={\bf \Delta_{T}}(I).} \end {aligned}
\left\{ \begin{array} { c } { k } \\ { i \ j } \\ \end{array} \right\} = \frac { 1 } { 2 } \gamma ^ { k l } ( \gamma _ { l i , j } + \gamma _ { l j , i } - \gamma _ { i j , l } ) , | \left \{ \begin {array} {c} {k} \\ {i\ j} \end {array} \right \} = \frac {1} {2} \gamma ^ {k l} ( \gamma _ {l i,j} + \gamma _ {l j,i} - \gamma _ {i j,l} ) ,
\begin{align*}X:=\overline{\oplus_{j=1}^\infty X_j},\quad \dim X_j<\infty.\end{align*} | \begin {aligned} {X:=\overline{\oplus_{j=1}^{\infty} X_{j}},\quad\dim X_{j}<\infty.} \end {aligned}
\begin{align*}E_i({\rm L})=\sum_{j=1}^n\frac{\sf d}{{\sf d}{x^j}}\left(\varpi^{ij}\left( A^j_i - A^i_j\right)\right)=0,\forall i=1,\dots,n.\end{align*} | \begin {aligned} {E_{i}(\mathrm{L})=\sum_{j=1}^{n} \frac{\sf d}{{\sf d}{x^{j}}} \left(\varpi^{i j} \left(A_{i}^{j}-A_{j}^{i} \right) \right)=0,\forall i=1,. . .,n.} \end {aligned}
\mathbf { r } | \mathbf { {r} }
\begin{align*}& \partial_{a_0}^m \partial_1^k G(a_0, x_1 - y, x_2)\\ = & \partial_{a_0}^m \partial_1^k \left( \frac{e^{-x_2}}{( 4 \pi a_0 x_2)^{\frac{1}{2}}} e^{-z^2} \right)\\ = & e^{-x_2} \partial_{a_0}^m \left( (a_0 x_2)^{-\frac{1 +k}{ 2} } P_k(z) e^{-z^2} \right)\\ = & e^{-x_2} P_{k + 2m}(z, a^{-\frac{1}{2}}_0) e^{-z^2} x_2^{-\frac{1 +k}{2}} .\end{align*} | \begin {aligned} & { \partial_{a_{0}}^{m} \partial_{1}^{k} G(a_{0},x_{1}-y,x_{2})} \\ =& { \partial_{a_{0}}^{m} \partial_{1}^{k} \left(\frac{e^{-x_{2}}}{(4 \pi a_{0} x_{2})^{\frac{1}{2}}} e^{-z^{2}} \right)} \\ =& { e^{-x_{2}} \partial_{a_{0}}^{m} \left((a_{0} x_{2})^{-\frac{1+k}{2}} P_{k}(z) e^{-z^{2}} \right)} \\ =& { e^{-x_{2}} P_{k+2 m}(z,a_{0}^{-\frac{1}{2}}) e^{-z^{2}} x_{2}^{-\frac{1+k}{2}}.} \end {aligned}
\langle \Pi _ { s , \Omega \Omega } ^ { H , \ell } \rangle | \langle \Pi _ {s,\Omega \Omega} ^ {H,\ell} \rangle
t = 0 | t= 0
- | -
\times | \times
U = D ( \omega - \lambda ) \, V _ { 2 3 } \, V _ { 1 3 } \, W _ { 1 2 } \, D ( \lambda ) \, . | U=D( \omega - \lambda ) \, V_ {2 3} \, V_ {1 3} \, W_ {1 2} \, D( \lambda ) \, .
\begin{align*} -f_k\left(z\right)-\left[\mathbf{D}\right]_{k,k}f_k\left(z\right)\underline{m}\left(z\right)=1.\end{align*} | \begin {aligned} {-f_{k} \left(z \right)-\left[\mathbf{D} \right]_{k,k} f_{k} \left(z \right) \underline{m} \left(z \right)=1.} \end {aligned}
\begin{align*} r_i \in (1-\delta,1+\delta) a_i = 1, r_i \in (0,\infty) a_i=0.\end{align*} | \begin {aligned} {r_{i} \in(1-\delta,1+\delta) a_{i}=1,r_{i} \in(0,\infty) a_{i}=0.} \end {aligned}
4 ^ { \circ } | 4 ^ {\circ}
\begin{align*}u_\beta(t)\beta^\vee(t)u_{-\alpha}(z)=u_{-\alpha}\left( t^{\frac{-2\langle \alpha,\beta\rangle}{\langle \beta,\beta \rangle}}z \right) u_\beta(t)\beta^\vee(t),\;\;\;\;\;\;\;t\in\mathbb{C}^\times, \, z \in {\mathbb{C}}.\end{align*} | \begin {aligned} {u_{\beta}(t) \beta^{\vee}(t) u_{-\alpha}(z)=u_{-\alpha} \left(t^{\frac{-2 \langle \alpha,\beta \rangle}{\langle \beta,\beta \rangle}} z \right) u_{\beta}(t) \beta^{\vee}(t),\;\;\;\;\;\;\;t \in \mathbb{C}^{\times},\,z \in{\mathbb{C}}.} \end {aligned}
K \rightarrow K - { \mathrm { l } n } { \tilde { f } } _ { \gamma } - { \mathrm { l } n } { \bar { \tilde { f } } _ { \gamma } } | K \rightarrow K- {\mathrm{l} n} {\tilde{f}} _ {\gamma} - {\mathrm{l} n} {\bar{\tilde{f}}_{\gamma}}
\begin{align*}\mathbf{R}_{1\bar{1}1\bar{2}}-\mathbf{R}_{1\bar{2}2\bar{2}}=0,\end{align*} | \begin {aligned} {\mathbf{R}_{1 \bar{1} 1 \bar{2}}-\mathbf{R}_{1 \bar{2} 2 \bar{2}}=0,} \end {aligned}
T = \Phi ^ { ( 2 ) } + \frac { i } { 2 } \frac { \partial _ { r } \Phi } { \partial \psi ^ { n } } ^ { ( 2 ) } \Phi _ { 3 n } ^ { ( 1 ) } \, \, . | T= \Phi ^ {(2)} + \frac {i} {2} \frac {\partial_{r} \Phi} {\partial \psi^{n}} ^ {(2)} \Phi _ {3 n} ^ {(1)} \, \, .
\partial ^ { \mu } { \cal S } \partial _ { \mu } S - 2 i m c { \cal D } \partial ^ { \mu } \partial _ { \mu } { \cal S } = m ^ { 2 } c ^ { 2 } , | \partial ^ {\mu} {\cal S} \partial _ {\mu} S- 2 i m c {\cal D} \partial ^ {\mu} \partial _ {\mu} {\cal S} =m^ {2} c^ {2} ,
1 3 8 | 1 3 8
\begin{align*}D^2 = 1 - \frac{N}{9} - \delta'^2 - \frac{9-N}\delta^2 = 0.\end{align*} | \begin {aligned} {D^{2}=1-\frac{N}{9}-\delta^{2}-\frac{9-N}{\delta}^{2}=0.} \end {aligned}
H _ { \mathrm { e f f } } = \sum _ { p \neq 0 } \widetilde { \epsilon } _ { p } \alpha _ { p } ^ { \dagger } \alpha _ { p } + \; E _ { 0 } \; , | H_ {\mathrm{eff}} = \sum _ {p \neq 0} \widetilde { {\epsilon} } _ {p} \alpha _ {p} ^ {\dagger} \alpha _ {p} + \; E_ {0} \; ,
\omega ^ { - 1 } ( x ) \tilde { F } _ { \mu \nu } ( x ) \omega ( x ) = - \frac { 2 } { \bar { N } } \epsilon _ { \mu \nu \rho \sigma } \int \delta \xi d s E ^ { \rho } [ \xi | s ] \dot { \xi } ^ { \sigma } ( s ) \dot { \xi } ^ { - 2 } ( s ) \delta ( x - \xi ( s ) ) . | \omega ^ {-1} ( x) \tilde { {F} } _ {\mu \nu} ( x) \omega ( x)=- \frac {2} {\bar{N}} \epsilon _ {\mu \nu \rho \sigma} \int \delta \xi d s E^ {\rho} [\xi | s] \dot { {\xi} } ^ {\sigma} ( s) \dot { {\xi} } ^ {-2} ( s) \delta ( x- \xi ( s)).
\begin{align*}\hat{P}_{n}(x^{2})=\frac{4^{n}}{n!\,(\theta)_{2n}}\, W_{n}\!\left(\frac{x^{2}}{4};-\frac{1}{4}+\frac{\theta}{2},\frac{1}{4},\frac{1}{4}+\frac{\theta}{2},\frac{3}{4}\right)\!.\end{align*} | \begin {aligned} {\hat{P}_{n}(x^{2})=\frac{4^{n}}{n !\,(\theta)_{2 n}}\,W_{n}\!\left(\frac{x^{2}}{4};-\frac{1}{4}+\frac{\theta}{2},\frac{1}{4},\frac{1}{4}+\frac{\theta}{2},\frac{3}{4} \right)\!.} \end {aligned}
\begin{align*}\partial_{t}h=P-h>0 {\rm for \ all }\ t\geq 0.\end{align*} | \begin {aligned} {\partial_{t} h=P-h>0 \mathrm{for\ all}\ t \geq 0.} \end {aligned}
J ^ { \mu } J ^ { \nu } = 8 \, e ^ { - 2 \phi } J ^ { \mu } { } _ { \eta } J ^ { \eta \nu } \ . | J^ {\mu} J^ {\nu} = 8 \, e^ {-2 \phi} J^ {\mu}  _ {\eta} J^ {\eta \nu} .
B | B
P = r _ { 1 } \vec { e } _ { 1 } ^ { * } + r _ { 2 } \vec { e } _ { 2 } ^ { * } | P=r_ {1} \vec { {e} } _ {1} ^ {*} +r_ {2} \vec { {e} } _ {2} ^ {*}
\psi = \sum _ { s } \psi _ { s } a _ { s } , | \psi = \sum _ {s} \psi _ {s} a_ {s} ,
\tau | \tau
\begin{align*} v = \sum_{1\leq j \leq n+1, j\neq i} v\cdot (\nabla \lambda_j) |e_{ij}| t_{ij}.\end{align*} | \begin {aligned} {v=\sum_{1 \leq j \leq n+1,j \neq i} v \cdot(\nabla \lambda_{j}) | e_{i j} | t_{i j}.} \end {aligned}
T = 1 0 0 | T= 1 0 0
\bar { R } ( u ) = R ( \bar { u } ) , \qquad \bar { u } = \theta - u , | \bar { {R} } ( u)=R( \bar { {u} } ) , \qquad \bar { {u} } = \theta -u,
{ \bf \mathrm { O T F } } = { \bf F } \, { \bf \mathrm { P S F } } | {\bf \mathrm{OTF}} = {\bf F} \, {\bf \mathrm{PSF}}
U ( r ) < \left( 4 \pi k _ { x _ { 0 } } ( r ) \varepsilon ^ { 2 } \right) ^ { \frac { 3 } { 2 } } | U(r)< \left ( 4 \pi k_ {x_{0}} ( r) \varepsilon ^ {2} \right ) ^ {\frac{3}{2}}
\left( 1 + \Lambda \frac { \partial } { \partial z } \right) \frac { \delta p } { p _ { 0 } } = \left( \gamma + \Lambda \frac { \partial } { \partial z } \right) \frac { V _ { z } } { v _ { A } } + O ( \epsilon ^ { 2 } ) . | \left ( 1 + \Lambda \frac {\partial} {\partial z} \right ) \frac {\delta p} {p_{0}} = \left ( \gamma + \Lambda \frac {\partial} {\partial z} \right ) \frac {V_{z}} {v_{A}} +O( \epsilon ^ {2} ) .
r _ { k } ( D _ { T } ) D _ { \mu } = D _ { \mu } r _ { k } ( - D ^ { 2 } ) , \quad \quad r _ { k } ( D _ { T } ) n _ { \mu } = n _ { \mu } r _ { k } ( - D ^ { 2 } ) \ , | r_ {k} ( D_ {T} ) D_ {\mu} =D_ {\mu} r_ {k} ( -D^ {2} ) , \quad \quadr _ {k} ( D_ {T} ) n_ {\mu} =n_ {\mu} r_ {k} ( -D^ {2} ) ,
\begin{align*}\mathbf{Z}_{\sf eff} = \sqrt{{\sf SNR}}\,\, \mathbf{L} \mathbf{W}\end{align*} | \begin {aligned} {\mathbf{Z}_{\sf e f f}=\sqrt{\sf S N R}\,\,\mathbf{L} \mathbf{W}} \end {aligned}
\begin{align*}(d\sigma_G)\circ (h) -(h^{-1})\circ d\sigma_G\,=\, 0\, ,\end{align*} | \begin {aligned} {(d \sigma_{G}) \circ(h)-(h^{-1}) \circ d \sigma_{G}\,=\,0\,,} \end {aligned}
\begin{array} { r } { \operatorname* { l i m s u p } _ { N \to \infty } \Bigl | \frac { 1 } { N } \sum _ { t = 1 } ^ { N ( N - 1 ) / 2 } \bigl ( \mathbb E \ln \bigl \langle e ^ { \beta \tilde { J } _ { i _ { t } j _ { t } } \sigma _ { i _ { t } } \sigma _ { j _ { t } } } \bigr \rangle _ { t } - \mathbb E \ln \bigl \langle e ^ { \beta J _ { i _ { t } j _ { t } } \sigma _ { i _ { t } } \sigma _ { j _ { t } } } \bigr \rangle _ { t } \bigr ) \Bigr | \leq \frac { \alpha \beta } { \alpha - 1 } K ^ { 1 - \alpha } . } \end{array} | \begin {array} {r} {{\operatorname*{l i m s u p}_{N \to \infty} \Bigl| \frac{1}{N} \sum_{t=1}^{N(N-1)/2} \bigl(\mathbb E \mathrm{ln} \bigl\langle  e^{\beta \tilde{J}_{i_{t} j_{t}} \sigma_{i_{t}} \sigma_{j_{t}}} \bigr\rangle _{t}-\mathbb E \mathrm{ln} \bigl\langle  e^{\beta J_{i_{t} j_{t}} \sigma_{i_{t}} \sigma_{j_{t}}} \bigr\rangle _{t} \bigr) \Bigr| \leq \frac{\alpha \beta}{\alpha-1} K^{1-\alpha}.}} \end {array}
R _ { r } + Z _ { p } | R_ {r} +Z_ {p}
( { \overline { Q } } ^ { 3 } Q _ { i } ) ( { \overline { Q } } ^ { 3 } Q _ { j } ) ~ , | ( {\overline{Q}} ^ {3} Q_ {i} ) ( {\overline{Q}} ^ {3} Q_ {j} ) ~,
\gamma _ { \tau } = \nu _ { \tau } ( 2 \tau - \eta _ { \tau } ) , | \gamma _ {\tau} = \nu _ {\tau} ( 2 \tau - \eta _ {\tau} ) ,
\begin{align*}Lu=\partial _j(a_{ij}\partial _i u )+ V\cdot \nabla u+du.\end{align*} | \begin {aligned} {L u=\partial_{j}(a_{i j} \partial_{i} u)+V \cdot \nabla u+d u.} \end {aligned}
\begin{align*} \tilde{\Lambda}_i(z)=p_+^i(z)=q_+^i(z)~ {\rm for} ~i\in I_w\cup I_b. \end{align*} | \begin {aligned} {\tilde{\Lambda}_{i}(z)=p_{+}^{i}(z)=q_{+}^{i}(z) ~ \mathrm{for} ~ i \in I_{w} \cup I_{b}.} \end {aligned}
\Delta : H ^ { 2 } ( { \mathbb { R } } ^ { n } ) \to L ^ { 2 } ( { \mathbb { R } } ^ { n } ) | \Delta :H^ {2} ( {\mathbb{R}} ^ {n} ) \to L^ {2} ( {\mathbb{R}} ^ {n} )
\begin{align*}E_k \triangleright w = \sum_{m = 1}^r E_k \triangleright (v_m \otimes f^m) = q^{1/2} v_{k} \otimes f^{k + 1} - q^{1/2} v_k \otimes f^{k + 1} = 0.\end{align*} | \begin {aligned} {E_{k} \triangleright w=\sum_{m=1}^{r} E_{k} \triangleright(v_{m} \otimes f^{m})=q^{1/2} v_{k} \otimes f^{k+1}-q^{1/2} v_{k} \otimes f^{k+1}=0.} \end {aligned}
\begin{align*}p_t^{T\Gamma}(Tx,Ty) = p_t^\Gamma (x,y), x,y \in \Gamma,\; t>0.\end{align*} | \begin {aligned} {p_{t}^{T \Gamma}(T x,T y)=p_{t}^{\Gamma}(x,y),x,y \in \Gamma,\;t>0.} \end {aligned}
\rho | \rho
\begin{array} { r l } { \delta ^ { 2 } S _ { n } = } & { - \frac { \hbar ^ { 2 } \tau _ { n } } { \mu } ( { \bf P } - P _ { x } \hat { x } ) ^ { 2 } + \frac { \partial ^ { 2 } S _ { \mathrm { s c } } } { \partial P _ { n } ^ { 2 } } \bar { P } ^ { 2 } + \frac { \partial ^ { 2 } S _ { \mathrm { s c } } ^ { ( t , \tau ) } } { \partial t _ { n } ^ { 2 } } \delta t ^ { 2 } } \\ & { + 2 \delta \tau \frac { \partial ^ { 2 } S _ { \mathrm { s c } } ^ { ( t , \tau ) } } { \partial \tau _ { n } \partial t _ { n } } \delta t + \frac { \partial ^ { 2 } S _ { \mathrm { s c } } ^ { ( t , \tau ) } } { \partial \tau _ { n } ^ { 2 } } \delta \tau ^ { 2 } . } \end{array} | \begin {array} {r l} {\delta^{2} S_{n}=} & {{-\frac{\hbar^{2} \tau_{n}}{\mu}({\bf P}-P_{x} \hat{x})^{2}+\frac{\partial^{2} S_{\mathrm{sc}}}{\partial P_{n}^{2}} \bar{P}^{2}+\frac{\partial^{2} S_{\mathrm{sc}}^{(t,\tau)}}{\partial t_{n}^{2}} \delta t^{2}}} \\ & {{+2 \delta \tau \frac{\partial^{2} S_{\mathrm{sc}}^{(t,\tau)}}{\partial \tau_{n} \partial t_{n}} \delta t+\frac{\partial^{2} S_{\mathrm{sc}}^{(t,\tau)}}{\partial \tau_{n}^{2}} \delta \tau^{2}.}} \end {array}
\begin{align*} \overline{g}((\nabla'_{X}\mathcal{A}_{\widehat{W}})Y,Z)=\overline{g}(\nabla'_{X}(\mathcal{A}_{\widehat{W}}Y),Z)-\overline{g}(\nabla'_{X}Y,\mathcal{A}_{\widehat{W}}Z). \end{align*} | \begin {aligned} {\overline{g}((\nabla_{X}^{\prime} \mathcal{A}_{\widehat{W}}) Y,Z)=\overline{g}(\nabla_{X}^{\prime}(\mathcal{A}_{\widehat{W}} Y),Z)-\overline{g}(\nabla_{X}^{\prime} Y,\mathcal{A}_{\widehat{W}} Z).} \end {aligned}
\begin{align*}h:=\phi\circ f\circ \phi^{-1}\colon \phi(f^{-1}(U)\cap U)\to V\end{align*} | \begin {aligned} {h:=\phi \circ f \circ \phi^{-1} \colon \phi(f^{-1}(U) \cap U) \to V} \end {aligned}
\begin{align*}\Vert J_{44}^{\left( 3\right) }\Vert _{p}=\mathcal{O}\left( \frac{1}{m}\right) (v(|z_{1}|)+(v(|z_{2}|))\left( \frac{\omega (h_{1})}{v(h_{1})}+\frac{\omega (h_{2})}{v(h_{2})}\right) . \end{align*} | \begin {aligned} {\Vert J_{4 4}^{\left(3 \right)} \Vert_{p}=\mathcal{O} \left(\frac{1}{m} \right)(v(| z_{1} |)+(v(| z_{2} |)) \left(\frac{\omega(h_{1})}{v(h_{1})}+\frac{\omega(h_{2})}{v(h_{2})} \right).} \end {aligned}
{ \cal H } _ { i n t } ( x ) = - e \overline { { \psi } } ^ { ( 0 ) } ( x ) \beta ^ { \mu } A _ { \mu } ^ { ( 0 ) } ( x ) [ I + \frac { e } { m } ( I - \beta _ { 0 } ^ { 2 } ) \beta ^ { \nu } A _ { \nu } ^ { ( 0 ) } ( x ) ] \psi ^ { ( 0 ) } ( x ) \, \, , | {\cal H} _ {i n t} ( x)=-e \overline { {\psi} } ^ {(0)} ( x) \beta ^ {\mu} A_ {\mu} ^ {(0)} ( x) [I+\frac{e}{m}(I-\beta_{0}^{2}) \beta^{\nu} A_{\nu}^{(0)}(x)] \psi ^ {(0)} ( x) \, \, ,
y ( t ) | y(t)
\begin{array} { r l } { \Gamma ( 0 , z ) } & { { } = \operatorname* { l i m } _ { s \to 0 } \left( \Gamma ( s ) - { \frac { 1 } { s } } - ( \gamma ( s , z ) - { \frac { 1 } { s } } ) \right) } \end{array} | \begin {array} {r l} {\Gamma(0,z)} & {{=\operatorname*{l i m}_{s \to 0} \left(\Gamma(s)-{\frac{1}{s}}-(\gamma(s,z)-{\frac{1}{s}}) \right)}} \end {array}
{ \bf \cal I } ^ { B } | {\bf \cal I} ^ {B}
\begin{array} { r l } { V _ { 1 } ^ { ( 1 ) } ( \theta , \lambda ) = } & { { } \frac { G M } { R _ { B } } [ 1 + \sum _ { n = 2 } ^ { M } ( \frac { a } { R _ { B } } ) ^ { n } \sum _ { m = 0 } ^ { n } ( \bar { C } _ { n m } ^ { 1 } \cos m \lambda } \end{array} | \begin {array} {r l} {V_{1}^{(1)}(\theta,\lambda)=} & {{ \frac{G M}{R_{B}}[1+\sum_{n=2}^{M}(\frac{a}{R_{B}})^{n} \sum_{m=0}^{n}(\bar{C}_{n m}^{1} \mathrm{cos} m \lambda}} \end {array}
\Delta ( \Omega _ { k k ^ { \prime } k ^ { \prime \prime } } ) = \int _ { 0 } ^ { t \gg 1 / \omega } e ^ { i \Omega _ { k k ^ { \prime } k ^ { \prime \prime } } t ^ { \prime } } d t ^ { \prime } = { \frac { e ^ { i \Omega _ { k k ^ { \prime } k ^ { \prime \prime } } t } - 1 } { i \Omega _ { k k ^ { \prime } k ^ { \prime \prime } } } } \, . | \Delta ( \Omega _ {k k^{\prime} k^{\prime \prime}} ) = \int _ {0} ^ {t \gg 1/\omega} e^ {i \Omega_{k k^{\prime} k^{\prime \prime}} t^{\prime}} d t^ {\prime} = {\frac{e^{i \Omega_{k k^{\prime} k^{\prime \prime}} t}-1}{i \Omega_{k k^{\prime} k^{\prime \prime}}}} \, .
\langle \kappa _ { 1 } \delta _ { L } | _ { \kappa \delta _ { L } } \rangle | \langle \kappa _ {1} \delta _ {L} |_ {\kappa \delta_{L}} \rangle
j | j
\begin{align*}A=\sum_{i=1}^{r}\sigma_iu_iv_i^T.\end{align*} | \begin {aligned} {A=\sum_{i=1}^{r} \sigma_{i} u_{i} v_{i}^{T}.} \end {aligned}
\hat { t } _ { A o A } \gtrsim 1 . 5 | \hat { {t} } _ {A o A} \gtrsim 1 . 5
f ( r ) | f(r)
\begin{array} { r l } { h ( \mathbf { r } , \phi ) : } & { \textbf { I n p u t } ( \mathbf { r } ) \to \textbf { L i n e a r } ( 3 , 1 0 0 ) \to \textbf { R e L U } } \\ & { \to \textbf { L i n e a r } ( 1 0 0 , 1 0 0 ) \to \textbf { R e L U } } \\ & { \to \textbf { L i n e a r } ( 1 0 0 , 1 0 0 ) \to \textbf { R e L U } } \\ & { \to \textbf { L i n e a r } ( 1 0 0 , 1 0 0 ) \to \textbf { R e L U } } \\ & { \to \textbf { L i n e a r } ( 1 0 0 , 3 ) \to \textbf { S o f t m a x } } \\ & { \to \textbf { S q r t } \to \textbf { O u t p u t } ( \mathbf { x } _ { \mathbf { r } } ) . } \end{array} | \begin {array} {r l} {h(\mathbf{r},\phi):} & {\textbf{I n p u t}(\mathbf{r}) \to \textbf{L i n e a r}(3,1 0 0) \to \textbf{R e L U}} \\ & {\to \textbf{L i n e a r}(1 0 0,1 0 0) \to \textbf{R e L U}} \\ & {\to \textbf{L i n e a r}(1 0 0,1 0 0) \to \textbf{R e L U}} \\ & {\to \textbf{L i n e a r}(1 0 0,1 0 0) \to \textbf{R e L U}} \\ & {\to \textbf{L i n e a r}(1 0 0,3) \to \textbf{S o f t m a x}} \\ & {{\to \textbf{S q r t} \to \textbf{O u t p u t}(\mathbf{x}_{\mathbf{r}}).}} \end {array}
\begin{align*}\mathbf{e}_ib=\begin{cases}b' & \mbox{if $b'\in\mathbf B$ satisfies $\mathbf{f}_i \,b'=b$,} \\0 & \mbox{if there exists no $b'\in\mathbf B$ such that$\mathbf{f}_i \,b'=b$.}\end{cases}\end{align*} | \begin {align*} \mathbf { {e} } _ib= \begin {cases} b' &__PROTECTED_TEXT_ 0 __ \\ 0 &__PROTECTED_TEXT_ 1 __ \end {cases} \end {align*}
u ^ { 2 } | u^ {2}
f _ { \mathrm { A } } ^ { \perp } = \mathbf { n } \cdot \mathbf { f } _ { \mathrm { A } } | f_ {\mathrm{A}} ^ {\perp} = \mathbf { {n} } \cdot \mathbf { {f} } _ {\mathrm{A}}
\begin{array} { r } { \frac { d } { d t } ( \Omega _ { 1 } + i \Omega _ { 2 } ) = - \phi \Omega _ { 3 } ( \Omega _ { 1 } + i \Omega _ { 2 } ) + i \frac { b } { I _ { 2 } } K _ { 3 } ; } \\ { \frac { d } { d t } ( K _ { 1 } + i K _ { 2 } ) = - i \Omega _ { 3 } ( K _ { 1 } + i K _ { 2 } ) + i ( \Omega _ { 1 } + i \Omega _ { 2 } ) K _ { 3 } . } \end{array} | \begin {array} {r} {{\frac{d}{d t}(\Omega_{1}+i \Omega_{2})=-\phi \Omega_{3}(\Omega_{1}+i \Omega_{2})+i \frac{b}{I_{2}} K_{3};}} \\ {\frac{d}{d t}(K_{1}+i K_{2})=-i \Omega_{3}(K_{1}+i K_{2})+i(\Omega_{1}+i \Omega_{2}) K_{3}.} \end {array}
\begin{align*}f(y)-f(x)&=h(1)-h(0)=\int_{0}^{1}\nabla h(\tau)d\tau=\int_{0}^1\langle y-x,\nabla f(x+\tau(y-x))\rangle d\tau\end{align*} | \begin {aligned} {f(y)-f(x)} & {=h(1)-h(0)=\int_{0}^{1} \nabla h(\tau) d \tau=\int_{0}^{1} \langle y-x,\nabla f(x+\tau(y-x)) \rangle d \tau} \end {aligned}
d _ { k } = \frac { 2 k } { k - 1 } , \qquad k = 2 , 3 , \cdots , \infty | d_ {k} = \frac {2 k} {k-1} , \qquadk = 2 , 3 , . . . , \infty
\begin{align*}s(x')\Omega_i=\Omega_i,s(x'')\Omega_j=\Omega_j.\end{align*} | \begin {aligned} {s(x^{\prime}) \Omega_{i}=\Omega_{i},s(x^{\prime \prime}) \Omega_{j}=\Omega_{j}.} \end {aligned}
\left( Z ^ { a } \right) ^ { \dagger } \eta _ { b } ^ { a } Z ^ { b } | \left ( Z^ {a} \right ) ^ {\dagger} \eta _ {b} ^ {a} Z^ {b}
C _ { T } | C_ {T}
\begin{align*} u_{4,j}v_{j+1}^* + av_j u_{1,j+1}^* -H_{4,j}L_{1,j+1}^*+(I-aT_j)L_{2,j+1}^*H_{1,j+1}=0. \end{align*} | \begin {aligned} {u_{4,j} v_{j+1}^{*}+a v_{j} u_{1,j+1}^{*}-H_{4,j} L_{1,j+1}^{*}+(I-a T_{j}) L_{2,j+1}^{*} H_{1,j+1}=0.} \end {aligned}
\begin{align*}G_{rt}(p) = {\left[ {\vec{p}}^{\,2} - {(p_0 + i0)}^2 \right]}^{-1} =\end{align*} | \begin {aligned} {G_{r t}(p)={\left[{\vec{p}}^{\,2}-{(p_{0}+i 0)}^{2} \right]}^{-1}=} \end {aligned}
\theta _ { i } | \theta _ {i}
