CONFIG = {
    'mode': 'normalize',
    'latex_string': 'F _ { { \mathbb T } _ { c } ^ { 2 } } \sim \frac { 1 } { \mathrm { v o l } ( { \mathbb T } _ { c } ^ { 2 } ) _ { h e t } } \, , ',
    'remove_trailing': False,
    'enable_synonym_replacement': False,
    'unify_environments': False,
}

import os
import re
import subprocess
import tempfile
import csv
from pathlib import Path

SKIP_PATTERNS = [r'\{', r'\}', r'[\[\]]', r'\\begin\{.*?\}', r'\\end\{.*?\}', r'\^', r'\_', r'\\.*rule.*', r'\\.*line.*', r'\[[\-.0-9]+[epm][xtm]\]']
SKIP_Tokens = ['\\', '\\\\', '\\index', '\\a', '&', '$', '\\multirow', '\\def', '\\edef', '\\raggedright', '\\url', '\\cr', '\\ensuremath', '\\left', '\\right',
               '\\mathchoice', '\\scriptstyle', '\\displaystyle', '\\qquad', '\\quad', '\\,', '\\!', '~', '\\boldmath', '\\gdef', '\\today', '\\the']
PHANTOM_Tokens = ['\\fontfamily', '\\vphantom', '\\phantom', '\\rowcolor', '\\ref', '\\thesubequation', '\\global', '\\theboldgroup']
TWO_Tail_Tokens = ['\\frac', '\\binom']
AB_Tail_Tokens = ['\\xrightarrow', '\\xleftarrow', '\\sqrt']
TWO_Tail_Invisb_Tokens = ['\\overset', '\\underset', '\\stackrel']
ONE_Tail_Tokens = ['\\widetilde', '\\overline', '\\hat', '\\widehat', '\\tilde', '\\Tilde', '\\dot', '\\bar', '\\vec', '\\underline', '\\underbrace', '\\check',
                   '\\breve', '\\Bar', '\\Vec', '\\mathring', '\\ddot', '\\Ddot', '\\dddot', '\\ddddot']
ONE_Tail_Invisb_Tokens = ['\\boldsymbol', '\\pmb', '\\textbf', '\\mathrm', '\\mathbf', '\\mathbb', '\\mathcal', '\\textmd', '\\texttt', '\\textnormal',
                          '\\textit', '\\textup', '\\mathop', '\\mathbin', '\\smash', '\\operatorname', '\\textrm', '\\mathfrak', '\\emph',
                          '\\textsf', '\\textsc']

SCRIPT_DIR = Path(__file__).parent
JS_DIR = SCRIPT_DIR / "js_scripts"

def find_matching_brace(sequence, start_index, brace=['{', '}']):
    left_brace, right_brace = brace
    depth = 0
    for i, char in enumerate(sequence[start_index:], start=start_index):
        if char == left_brace:
            depth += 1
        elif char == right_brace:
            depth -= 1
            if depth == 0:
                return i
    if depth > 0:
        print(f"Warning! found no matching brace in sequence starting at {start_index}")
    return -1

def merge_tokens_with_pattern(text, pattern, process_func=None, add_trailing_space=False):
    old_tokens = re.findall(pattern, text, re.DOTALL)
    if not old_tokens:
        return text

    if process_func is None:
        process_func = lambda x: x.replace(" ", "")

    for old_token in old_tokens:
        new_token = process_func(old_token)
        if add_trailing_space and not new_token.endswith(" "):
            new_token += " "
        text = text.replace(old_token, new_token)

    return text

def clean_braces(text):
    result = text

    # 删除空花括号
    prev_result = ""
    while prev_result != result:
        prev_result = result
        result = re.sub(r'\{\s*\}', '', result)

    # 删除多余的嵌套花括号
    latex_commands = ['sqrt', 'frac', 'mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt',
                     'text', 'textbf', 'textit', 'textrm', 'textsf', 'texttt', 'textsc', 'hat', 'tilde', 'bar', 'vec',
                     'dot', 'ddot', 'overline', 'underline', 'sin', 'cos', 'tan', 'sec', 'csc', 'cot', 'sinh', 'cosh',
                     'tanh', 'arcsin', 'arccos', 'arctan', 'ln', 'log', 'exp', 'min', 'max']

    prev_result = ""
    iterations = 0
    while prev_result != result and iterations < 5:
        prev_result = result

        for cmd in latex_commands:
            pattern = f'\\\\{cmd}\\{{\\{{([^{{}}]+)\\}}\\}}'
            replacement = f'\\\\{cmd}{{{r"\1"}}}'
            result = re.sub(pattern, replacement, result)

        result = re.sub(r'\{\{([^{}]+)\}\}', r'{\1}', result)
        iterations += 1

    return result

def fix_align_environment(text):
    if '\\begin{align' not in text:
        return text

    result = text
    result = re.sub(r'(\\begin{align\*?})\s*\{\s*\}', r'\1', result)
    result = re.sub(r'\{\s*\}\s*(\\end{align\*?})', r'\1', result)
    result = re.sub(r'\}\s*\\\\\s*\{\s*\}', r'\\\\', result)
    result = re.sub(r'\\\\\s*\{\s*\}', r'\\\\', result)
    result = re.sub(r'\{\s*\}\s*\\\\', r'\\\\', result)
    result = re.sub(r'&\s*\{\s*\}', r'&', result)
    result = re.sub(r'\{\s*\}\s*&', r'&', result)
    result = re.sub(r'\^\s*\{\s*\}\s*', r'^{}', result)
    result = re.sub(r'\\\\\s*&=', r'\\\\ &=', result)
    result = re.sub(r'\s+', ' ', result)
    result = re.sub(r'^\s+|\s+$', '', result)

    # 括号平衡检查
    open_braces = result.count('{')
    close_braces = result.count('}')

    if open_braces > close_braces:
        missing_braces = open_braces - close_braces
        align_end_pos = result.rfind('\\end{align')
        if align_end_pos != -1:
            result = result[:align_end_pos] + '}' * missing_braces + result[align_end_pos:]
        else:
            result = result + '}' * missing_braces
    elif close_braces > open_braces:
        missing_braces = close_braces - open_braces
        align_start_pos = result.find('\\begin{align')
        if align_start_pos != -1:
            result = result[:align_start_pos] + '{' * missing_braces + result[align_start_pos:]
        else:
            result = '{' * missing_braces + result

    return result

def remove_trailing_latex(formula):
    pattern = r'(\\(hspace\*?\{[^{}]*?\}|vspace\*?\{[^{}]*?\}|smallskip|medskip|quad|qquad|bigskip|[;,])|\~|\.)*$'
    cleaned_formula = re.sub(pattern, '', formula, count=1)
    return cleaned_formula

def latex_aware_tokenizer(text):
    tokens = []
    i = 0

    while i < len(text):
        if text[i] == ' ' and (i == 0 or text[i-1] != '\\'):
            i += 1
            continue

        if text[i] == '\\':
            token, next_i = _parse_latex_command(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        if text[i] == '{':
            token, next_i = _parse_brace_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        if text[i] == '[':
            token, next_i = _parse_bracket_group(text, i)
            if token:
                tokens.append(token)
            i = next_i
            continue

        if text[i] in '()[]':
            tokens.append(text[i])
            i += 1
            continue

        token, next_i = _parse_normal_sequence(text, i)
        if token:
            tokens.append(token)
        i = next_i

    return [t for t in tokens if t.strip()]

def _parse_latex_command(text, start):
    if start >= len(text) or text[start] != '\\':
        return None, start + 1

    i = start + 1

    if i < len(text) and text[i] in '{}[]$%&#_':
        return f'\\{text[i]}', i + 1

    if i < len(text) and text[i] == '\\':
        return '\\\\', i + 1

    if i < len(text) and text[i] in ',:;!|=^.~"\'`()/-<>':
        return f'\\{text[i]}', i + 1

    if i < len(text) and text[i] == ' ':
        if i + 1 < len(text) and text[i + 1] in '+\\<>*_[]{}':
            return f'\\ {text[i + 1]}', i + 2
        else:
            return '\\ ', i + 1

    command = '\\'
    while i < len(text) and (text[i].isalpha() or text[i] in '*'):
        command += text[i]
        i += 1

    math_commands = ['\\mathrm', '\\mathbf', '\\mathit', '\\mathcal', '\\mathbb', '\\mathfrak', '\\mathsf', '\\mathtt']
    if command in math_commands:
        if i < len(text) and text[i] not in '{[ ':
            param_char = text[i]
            return command, i

    return command, i

def _parse_brace_group(text, start):
    if start >= len(text) or text[start] != '{':
        return None, start + 1

    brace_count = 1
    i = start + 1

    while i < len(text) and brace_count > 0:
        if text[i] == '{':
            brace_count += 1
        elif text[i] == '}':
            brace_count -= 1
        i += 1

    if brace_count == 0:
        return text[start:i], i
    else:
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_bracket_group(text, start):
    if start >= len(text) or text[start] != '[':
        return None, start + 1

    bracket_count = 1
    i = start + 1

    while i < len(text) and bracket_count > 0:
        if text[i] == '[':
            bracket_count += 1
        elif text[i] == ']':
            bracket_count -= 1
        i += 1

    if bracket_count == 0:
        return text[start:i], i
    else:
        while i < len(text) and text[i] != ' ':
            i += 1
        return text[start:i], i

def _parse_normal_sequence(text, start):
    i = start

    if text[i].isdigit():
        if len(text) > start + 1 and text[start + 1].isdigit():
            i = start + 1
        else:
            while i < len(text) and text[i].isdigit():
                i += 1
    else:
        while i < len(text) and text[i] not in '\\{}[] ' and not text[i].isdigit():
            i += 1

    if i > start:
        return text[start:i], i
    else:
        return None, start + 1

def tokenize_latex(latex_code):
    if not latex_code:
        return False, latex_code

    # 转换 mbox/hbox 为 text
    latex_code = re.sub(r'\\(mbox|hbox)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}', r'\\text{\2}', latex_code)

    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
        temp_filename = temp_file.name

        prepre = latex_code
        prepre = re.sub(r'\\begin{(split|align|alignedat|alignat|eqnarray)\*?}((?:(?!\\begin{|\\end{).)*?)\\end{\1\*?}',
                       r'\\begin{aligned}\2\\end{aligned}', prepre, flags=re.S)

        if CONFIG.get('unify_environments', False):
            prepre = re.sub(r'\\begin{(smallmatrix)\*?}(.+?)\\end{\1\*?}',
                           r'\\begin{matrix}\2\\end{matrix}', prepre, flags=re.S)

        temp_file.write(prepre)

    try:
        js_script = JS_DIR / "preprocess_formula.js"
        if not js_script.exists():
            return True, prepre

        if os.name == 'nt':
            cmd = f'type "{temp_filename}" | node "{js_script}" normalize'
        else:
            cmd = f'cat "{temp_filename}" | node "{js_script}" normalize'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                              timeout=30, encoding='utf-8', errors='replace')

        if result.returncode != 0:
            return True, prepre

        output = result.stdout
        if output is None:
            return True, prepre

        output = output.strip()
        if not output:
            return True, prepre

        # 操作符名称规范化
        operators = '|'.join(['arccos', 'arcsin', 'arctan', 'arg', 'cos', 'cosh', 'cot', 'coth', 'csc', 'deg', 'det', 'dim', 'exp', 'gcd', 'hom', 'inf',
                             'injlim', 'ker', 'lg', 'lim', 'liminf', 'limsup', 'ln', 'log', 'max', 'min', 'Pr', 'projlim', 'sec', 'sin', 'sinh', 'sup', 'tan', 'tanh'])
        ops = re.compile(r'\\operatorname {(' + operators + ')}')

        names = ['\\' + x.replace(' ', '') for x in re.findall(ops, output)]
        if names:
            output = re.sub(ops, lambda match: names.pop(0), output)
        output = output.replace(r'\\ \end{array}', r'\end{array}')

        return True, output

    except subprocess.TimeoutExpired:
        return True, prepre
    except Exception as e:
        return True, prepre
    finally:
        try:
            os.unlink(temp_filename)
        except:
            pass

def clean_spaces(text):
    result = text

    # 保护LaTeX换行符
    result = result.replace('\\\\', '__LATEX_NEWLINE__')

    # 清理结构性空格
    result = re.sub(r'\s*\{\s*', '{', result)
    result = re.sub(r'\s*\}', '}', result)
    result = re.sub(r'\s*\[\s*', '[', result)
    result = re.sub(r'\s*\]', ']', result)
    result = re.sub(r'\s*\(\s*', '(', result)
    result = re.sub(r'\s*\)', ')', result)

    # 清理运算符空格
    operators = ['+', '-', '=', '<', '>', '*', '/', '^', '_', ',', ';', ':', '.']
    for op in operators:
        result = re.sub(f'\\s*{re.escape(op)}\\s*', op, result)

    # 清理LaTeX命令空格
    result = re.sub(r'(\\[A-Za-z]+)\s*\{', r'\1{', result)
    result = re.sub(r'(\\[A-Za-z]+)\s*\[', r'\1[', result)

    # 清理数学字体命令内部空格
    math_fonts = ['mathrm', 'mathbf', 'mathit', 'mathcal', 'mathbb', 'mathfrak', 'mathsf', 'mathtt']
    for font in math_fonts:
        result = re.sub(f'\\\\{font}\\s*\\{{\\s*([^}}]+)\\s*\\}}', f'\\\\{font}{{\\1}}', result)

    # 添加必要空格
    space_required_commands = ['\\bf', '\\it', '\\rm', '\\sf', '\\tt', '\\sc', '\\em', '\\sl',
                              '\\tiny', '\\scriptsize', '\\footnotesize', '\\small', '\\normalsize',
                              '\\large', '\\Large', '\\LARGE', '\\huge', '\\Huge', '\\displaystyle',
                              '\\textstyle', '\\scriptstyle', '\\scriptscriptstyle', '\\quad', '\\qquad']

    for cmd in sorted(space_required_commands, key=len, reverse=True):
        escaped_cmd = re.escape(cmd)
        pattern = f'(?<![\\\\a-zA-Z])({escaped_cmd})(?=[a-zA-Z])'
        result = re.sub(pattern, r'\1 ', result)

    # 为\mathrm{}后面直接跟字母的情况添加空格
    result = re.sub(r'(\\mathrm\{[^}]+\})([a-zA-Z])', r'\1 \2', result)

    # 为希腊字母等LaTeX命令后添加空格
    greek_letters = ['\\alpha', '\\beta', '\\gamma', '\\delta', '\\epsilon', '\\zeta', '\\eta', '\\theta',
                    '\\iota', '\\kappa', '\\lambda', '\\mu', '\\nu', '\\xi', '\\pi', '\\rho', '\\sigma',
                    '\\tau', '\\upsilon', '\\phi', '\\chi', '\\psi', '\\omega', '\\Gamma', '\\Delta',
                    '\\Theta', '\\Lambda', '\\Xi', '\\Pi', '\\Sigma', '\\Upsilon', '\\Phi', '\\Psi', '\\Omega']

    for letter in sorted(greek_letters, key=len, reverse=True):
        escaped_letter = re.escape(letter)
        pattern = f'(?<![a-zA-Z\\\\])({escaped_letter})([a-zA-Z])'
        result = re.sub(pattern, r'\1 \2', result)

    # 清理连续空格
    result = re.sub(r'\s{2,}', ' ', result)
    result = re.sub(r'^\s+|\s+$', '', result)

    # 恢复LaTeX换行符
    result = result.replace('__LATEX_NEWLINE__', '\\\\')

    return result

def process_math_functions(text):
    result = text

    # 处理operatorname格式
    math_functions = ['sin', 'cos', 'tan', 'cot', 'sec', 'csc', 'sinh', 'cosh', 'tanh', 'coth',
                     'arcsin', 'arccos', 'arctan', 'ln', 'lg', 'log', 'exp', 'min', 'max', 'mod',
                     'deg', 'arg', 'dim', 'ker', 'hom']

    for func in math_functions:
        # 处理 \operatorname{func} 格式
        result = re.sub(f'\\\\operatorname\\s*\\{{\\s*{func}\\s*\\}}', f'\\\\mathrm{{{func}}}', result)
        # 处理分隔的格式，如 \operatorname{s i n}
        spaced_func = ' '.join(func)
        result = re.sub(f'\\\\operatorname\\s*\\{{\\s*{spaced_func}\\s*\\}}', f'\\\\mathrm{{{func}}}', result)

    # 处理直接的函数名
    result = " " + result + " "
    for func in math_functions:
        result = re.sub(f'(?<=\\s)\\\\{func}(?=\\s)', f'\\\\mathrm{{{func}}}', result)

    result = result.strip()
    return result

def normalize_latex(l, rm_trail=False):
    if rm_trail:
        l = remove_trailing_latex(l)

    l = l.strip().replace(r'\pmatrix', r'\mypmatrix').replace(r'\matrix', r'\mymatrix')

    for item in ['\\raggedright', '\\arraybackslash', '\\lowercase', '\\uppercase']:
        l = l.replace(item, "")

    l = clean_spaces(l)
    # Token合并处理
    l = merge_tokens_with_pattern(l, r'\\[hv]space { [.0-9a-z ]+ }')

    def process_array_format(token):
        return token.replace("\\begin{array} ", "<s>").replace(" ", "").replace("<s>", "\\begin{array} ")
    l = merge_tokens_with_pattern(l, r'\\begin{array} { [lrc ]+ }', process_array_format)

    l = merge_tokens_with_pattern(l, r'\\string [^ ]+ ', add_trailing_space=True)
    l = merge_tokens_with_pattern(l, r'\\[Bb]ig[g]?[glrm]? [(){}|\[\]] ', add_trailing_space=True)
    l = merge_tokens_with_pattern(l, r'\\operatorname \*', lambda x: "\\operatorname")

    # 移除有害命令
    l = l.replace("\\lefteqn", "").replace("\\footnote ", "^ ")

    # 重音符号合并
    l = merge_tokens_with_pattern(l, r'\\\' [^{] ', add_trailing_space=True)

    # 其他命令合并
    l = merge_tokens_with_pattern(l, r'\\parbox {[^{]+}')
    l = merge_tokens_with_pattern(l, r'\\rule {[ .0-9a-z]+} {[ .0-9a-z]+}')
    l = merge_tokens_with_pattern(l, r'\\specialrule {[ .0-9a-z]+} {[ .0-9a-z]+} {[ .0-9a-z]+}')

    # 颜色命令移除
    pattern = r'\\colorbox[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\color[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\textcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } |\\cellcolor[ \[\]RGBrgb]+{ [A-Za-z 0-9,!]+ } '
    old_token = re.findall(pattern, l, re.DOTALL)
    for bef in old_token:
        l = l.replace(bef, "")

    # 括号补全处理
    l_split = latex_aware_tokenizer(l)
    idx = 0

    while idx < len(l_split):
        token = l_split[idx]
        if token in ONE_Tail_Tokens + ONE_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                l_split.insert(idx + 1, "{")
                l_split.insert(idx + 3, "}")
                idx += 2
        elif token in TWO_Tail_Tokens:
            if idx + 1 < len(l_split):
                current_pos = idx + 1
                if not (l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}")):
                    if l_split[current_pos] != "{":
                        l_split.insert(current_pos, "{")
                        l_split.insert(current_pos + 2, "}")
                        current_pos += 3
                    else:
                        current_pos = find_matching_brace(l_split, current_pos) + 1

                if current_pos < len(l_split):
                    if not (l_split[current_pos].startswith("{") and l_split[current_pos].endswith("}")):
                        if l_split[current_pos] != "{":
                            l_split.insert(current_pos, "{")
                            l_split.insert(current_pos + 2, "}")
                        else:
                            current_pos = find_matching_brace(l_split, current_pos) + 1
                idx = current_pos - 1
        elif token in TWO_Tail_Invisb_Tokens:
            if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                l_split.insert(idx + 1, "{")
                l_split.insert(idx + 3, "}")
                idx += 2
            if idx + 3 < len(l_split) and l_split[idx + 3] != "{":
                l_split.insert(idx + 3, "{")
                l_split.insert(idx + 5, "}")
                idx += 2
        elif token in AB_Tail_Tokens:
            if idx + 1 < len(l_split):
                if l_split[idx + 1] == "[":
                    bracket_end = find_matching_brace(l_split, idx + 1, brace=['[', ']'])
                    if bracket_end != -1:
                        idx = bracket_end
                if idx + 1 < len(l_split) and l_split[idx + 1] != "{":
                    l_split.insert(idx + 1, "{")
                    l_split.insert(idx + 3, "}")
                    idx += 2
        idx += 1

    l = ' '.join(l_split)
    l = process_math_functions(l)
    


    # 连字符展开和省略号统一化
    l = re.sub(r'---', r'- - -', l)
    l = re.sub(r'--', r'- -', l)

    # 省略号统一化
    dots_patterns = ['\\dotsc', '\\dotsi', '\\dotsm', '\\dotso', '\\dotsb', '…', '\\ldots',
                    '\\hdots', '\\cdots', '\\dddot', '\\dots', '\\mathellipsis']
    for pattern in dots_patterns:
        l = re.sub(re.escape(pattern), '. . .', l)

    # 清理花括号
    l = clean_braces(l)
    l = fix_align_environment(l)

    return l.strip()

def tokenize_latex_string(latex_string):
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            return latex_string
        return tokenized_latex
    except Exception as e:
        print(f"tokenize处理出错: {e}")
        return latex_string

def replace_synonym_tokens(tokenized_string):
    if not tokenized_string or not tokenized_string.strip():
        return tokenized_string

    if not CONFIG.get('enable_synonym_replacement', True):
        return tokenized_string

    csv_file_path = SCRIPT_DIR / "token_unify.csv"
    if not csv_file_path.exists():
        return tokenized_string

    try:
        synonym_map = {}
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            csv_reader = csv.reader(csvfile)
            for row in csv_reader:
                if row and len(row) >= 2:
                    synonym = row[0].strip()
                    standard_token = row[1].strip()
                    if synonym and standard_token:
                        synonym_map[synonym] = standard_token

        if not synonym_map:
            return tokenized_string

        result = tokenized_string
        for synonym in sorted(synonym_map.keys(), key=len, reverse=True):
            if synonym in result:
                result = result.replace(synonym, synonym_map[synonym])

        return result
    except Exception as e:
        return tokenized_string

def normalize_latex_string(latex_string):
    if not latex_string or not latex_string.strip():
        return latex_string

    try:
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            tokenized_latex = latex_string

        normalized_latex = normalize_latex(tokenized_latex, CONFIG.get('remove_trailing', False))
        return normalized_latex
    except Exception as e:
        print(f"规范化处理出错: {e}")
        return latex_string

def validate_config():
    errors = []
    if 'mode' not in CONFIG:
        errors.append("缺少必需参数: 'mode'")
    elif CONFIG['mode'] not in ['tokenize', 'normalize']:
        errors.append(f"无效的mode值: '{CONFIG['mode']}'，应为: 'tokenize', 'normalize'")

    if 'latex_string' not in CONFIG or not CONFIG['latex_string']:
        errors.append("需要设置 'latex_string' 参数")

    if errors:
        print("❌ 配置错误:")
        for error in errors:
            print(f"  - {error}")
        return False
    return True

def process_latex():
    latex_string = CONFIG['latex_string']
    mode = CONFIG['mode']

    if mode == 'tokenize':
        tokenized_result = tokenize_latex_string(latex_string)
        result = replace_synonym_tokens(tokenized_result)
    elif mode == 'normalize':
        success, tokenized_latex = tokenize_latex(latex_string)
        if not success:
            tokenized_latex = latex_string

        synonym_replaced_latex = replace_synonym_tokens(tokenized_latex)
        result = normalize_latex(synonym_replaced_latex, CONFIG.get('remove_trailing', False))
    else:
        print(f"未知的模式: {mode}")
        return latex_string

    print("=" * 80)
    print("LaTeX 规范化处理结果")
    print("=" * 80)
    print(f"输入: {latex_string}")
    print(f"输出: {result}")
    print("=" * 80)

    return result

def main():
    if not validate_config():
        print("配置错误，请修正后重新运行")
        return
    process_latex()

if __name__ == "__main__":
    main()
